{"version": 3, "sources": ["../../../src/client/legacy/image.tsx"], "names": ["React", "useRef", "useEffect", "useCallback", "useContext", "useMemo", "useState", "Head", "imageConfigDefault", "VALID_LOADERS", "useIntersection", "ImageConfigContext", "warnOnce", "normalizePathTrailingSlash", "normalizeSrc", "src", "slice", "DEFAULT_Q", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "loadedImageURLs", "Set", "allImgs", "Map", "perfObserver", "emptyDataURL", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "VALID_LOADING_VALUES", "undefined", "imgixLoader", "config", "width", "quality", "url", "URL", "path", "params", "searchParams", "set", "getAll", "join", "get", "toString", "href", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramsString", "customLoader", "Error", "defaultLoader", "NODE_ENV", "<PERSON><PERSON><PERSON><PERSON>", "push", "length", "JSON", "stringify", "startsWith", "localPatterns", "NEXT_RUNTIME", "hasLocalMatch", "require", "domains", "remotePatterns", "parsedSrc", "err", "console", "error", "hasRemoteMatch", "hostname", "qualities", "includes", "q", "reduce", "prev", "cur", "Math", "abs", "endsWith", "dangerouslyAllowSVG", "encodeURIComponent", "loaders", "VALID_LAYOUT_VALUES", "isStaticRequire", "default", "isStaticImageData", "isStaticImport", "getWidths", "layout", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "parseInt", "smallestRatio", "min", "widths", "filter", "s", "kind", "map", "w", "find", "p", "generateImgAttrs", "unoptimized", "loader", "srcSet", "last", "i", "getInt", "x", "defaultImageLoader", "loaderProps", "loader<PERSON>ey", "load", "handleLoading", "img", "placeholder", "onLoadingCompleteRef", "setBlurComplete", "decode", "Promise", "resolve", "catch", "then", "parentNode", "add", "current", "naturalWidth", "naturalHeight", "parentElement", "parent", "getComputedStyle", "position", "display", "ImageElement", "imgAttributes", "heightInt", "widthInt", "qualityInt", "className", "imgStyle", "blurStyle", "isLazy", "loading", "srcString", "setIntersection", "onLoad", "onError", "isVisible", "noscriptSizes", "rest", "decoding", "data-nimg", "style", "ref", "complete", "event", "currentTarget", "noscript", "Image", "priority", "lazyRoot", "lazyBoundary", "height", "objectFit", "objectPosition", "onLoadingComplete", "blurDataURL", "all", "configContext", "c", "imageSizes", "sort", "a", "b", "customImageLoader", "obj", "_", "opts", "staticSrc", "staticImageData", "has", "blurComplete", "isIntersected", "resetIntersected", "rootRef", "rootMargin", "disabled", "wrapperStyle", "boxSizing", "overflow", "background", "opacity", "border", "margin", "padding", "sizerStyle", "hasSizer", "sizerSvgUrl", "layoutStyle", "top", "left", "bottom", "right", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "String", "isNaN", "VALID_BLUR_EXT", "urlStr", "pathname", "search", "overwrittenStyles", "Object", "keys", "key", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "observe", "type", "buffered", "assign", "backgroundSize", "backgroundPosition", "backgroundImage", "quotient", "paddingTop", "fullUrl", "e", "location", "linkProps", "imageSrcSet", "crossOrigin", "referrerPolicy", "useLayoutEffect", "previousImageSrc", "imgElementArgs", "span", "alt", "aria-hidden", "link", "rel", "as"], "mappings": "AAAA;;AAEA,OAAOA,SACLC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,QACH,QAAO;AACd,OAAOC,UAAU,wBAAuB;AACxC,SACEC,kBAAkB,EAClBC,aAAa,QACR,gCAA+B;AAKtC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,kBAAkB,QAAQ,uDAAsD;AACzF,SAASC,QAAQ,QAAQ,mCAAkC;AAC3D,SAASC,0BAA0B,QAAQ,8BAA6B;AAExE,SAASC,aAAaC,GAAW;IAC/B,OAAOA,GAAG,CAAC,EAAE,KAAK,MAAMA,IAAIC,KAAK,CAAC,KAAKD;AACzC;AACA,MAAME,YAAY;AAClB,MAAMC,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAC/C,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAIpB,IAAIC;AACJ,MAAMC,eACJ;AAEF,IAAI,OAAOC,WAAW,aAAa;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAEA,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAqBzD,SAASC,YAAY,KAKQ;IALR,IAAA,EACnBC,MAAM,EACNnB,GAAG,EACHoB,KAAK,EACLC,OAAO,EACoB,GALR;IAMnB,qEAAqE;IACrE,MAAMC,MAAM,IAAIC,IAAI,AAAC,KAAEJ,OAAOK,IAAI,GAAGzB,aAAaC;IAClD,MAAMyB,SAASH,IAAII,YAAY;IAE/B,oEAAoE;IACpED,OAAOE,GAAG,CAAC,QAAQF,OAAOG,MAAM,CAAC,QAAQC,IAAI,CAAC,QAAQ;IACtDJ,OAAOE,GAAG,CAAC,OAAOF,OAAOK,GAAG,CAAC,UAAU;IACvCL,OAAOE,GAAG,CAAC,KAAKF,OAAOK,GAAG,CAAC,QAAQV,MAAMW,QAAQ;IAEjD,IAAIV,SAAS;QACXI,OAAOE,GAAG,CAAC,KAAKN,QAAQU,QAAQ;IAClC;IAEA,OAAOT,IAAIU,IAAI;AACjB;AAEA,SAASC,aAAa,KAIO;IAJP,IAAA,EACpBd,MAAM,EACNnB,GAAG,EACHoB,KAAK,EACsB,GAJP;IAKpB,OAAO,AAAC,KAAED,OAAOK,IAAI,GAAGzB,aAAaC,OAAK,cAAWoB;AACvD;AAEA,SAASc,iBAAiB,KAKG;IALH,IAAA,EACxBf,MAAM,EACNnB,GAAG,EACHoB,KAAK,EACLC,OAAO,EACoB,GALH;IAMxB,sFAAsF;IACtF,MAAMI,SAAS;QAAC;QAAU;QAAW,OAAOL;QAAO,OAAQC,CAAAA,WAAW,MAAK;KAAG;IAC9E,MAAMc,eAAeV,OAAOI,IAAI,CAAC,OAAO;IACxC,OAAO,AAAC,KAAEV,OAAOK,IAAI,GAAGW,eAAepC,aAAaC;AACtD;AAEA,SAASoC,aAAa,KAAyB;IAAzB,IAAA,EAAEpC,GAAG,EAAoB,GAAzB;IACpB,MAAM,IAAIqC,MACR,AAAC,qBAAkBrC,MAAI,gCACpB;AAEP;AAEA,SAASsC,cAAc,KAKM;IALN,IAAA,EACrBnB,MAAM,EACNnB,GAAG,EACHoB,KAAK,EACLC,OAAO,EACoB,GALN;QAuFnBF;IAjFF,IAAIf,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,MAAMC,gBAAgB,EAAE;QAExB,yDAAyD;QACzD,IAAI,CAACxC,KAAKwC,cAAcC,IAAI,CAAC;QAC7B,IAAI,CAACrB,OAAOoB,cAAcC,IAAI,CAAC;QAE/B,IAAID,cAAcE,MAAM,GAAG,GAAG;YAC5B,MAAM,IAAIL,MACR,AAAC,sCAAmCG,cAAcX,IAAI,CACpD,QACA,gGAA+Fc,KAAKC,SAAS,CAC7G;gBAAE5C;gBAAKoB;gBAAOC;YAAQ;QAG5B;QAEA,IAAIrB,IAAI6C,UAAU,CAAC,OAAO;YACxB,MAAM,IAAIR,MACR,AAAC,0BAAuBrC,MAAI;QAEhC;QAEA,IAAIA,IAAI6C,UAAU,CAAC,QAAQ1B,OAAO2B,aAAa,EAAE;YAC/C,IACE1C,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJC,aAAa,EACd,GAAGC,QAAQ;gBACZ,IAAI,CAACD,cAAc7B,OAAO2B,aAAa,EAAE9C,MAAM;oBAC7C,MAAM,IAAIqC,MACR,AAAC,uBAAoBrC,MAAI,kGACtB;gBAEP;YACF;QACF;QAEA,IAAI,CAACA,IAAI6C,UAAU,CAAC,QAAS1B,CAAAA,OAAO+B,OAAO,IAAI/B,OAAOgC,cAAc,AAAD,GAAI;YACrE,IAAIC;YACJ,IAAI;gBACFA,YAAY,IAAI7B,IAAIvB;YACtB,EAAE,OAAOqD,KAAK;gBACZC,QAAQC,KAAK,CAACF;gBACd,MAAM,IAAIhB,MACR,AAAC,0BAAuBrC,MAAI;YAEhC;YAEA,IACEI,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJS,cAAc,EACf,GAAGP,QAAQ;gBACZ,IAAI,CAACO,eAAerC,OAAO+B,OAAO,EAAE/B,OAAOgC,cAAc,EAAEC,YAAY;oBACrE,MAAM,IAAIf,MACR,AAAC,uBAAoBrC,MAAI,kCAAiCoD,UAAUK,QAAQ,GAAC,gEAC1E;gBAEP;YACF;QACF;QAEA,IAAIpC,WAAWF,OAAOuC,SAAS,IAAI,CAACvC,OAAOuC,SAAS,CAACC,QAAQ,CAACtC,UAAU;YACtE,MAAM,IAAIgB,MACR,AAAC,2BAAwBhB,UAAQ,8FAC9B;QAEP;IACF;IAEA,MAAMuC,IACJvC,aACAF,oBAAAA,OAAOuC,SAAS,qBAAhBvC,kBAAkB0C,MAAM,CAAC,CAACC,MAAMC,MAC9BC,KAAKC,GAAG,CAACF,MAAM7D,aAAa8D,KAAKC,GAAG,CAACH,OAAO5D,aAAa6D,MAAMD,UAEjE5D;IAEF,IAAIF,IAAIkE,QAAQ,CAAC,WAAW,CAAC/C,OAAOgD,mBAAmB,EAAE;QACvD,yDAAyD;QACzD,+CAA+C;QAC/C,OAAOnE;IACT;IAEA,OAAO,AAAGF,2BAA2BqB,OAAOK,IAAI,IAAE,UAAO4C,mBACvDpE,OACA,QAAKoB,QAAM,QAAKwC;AACpB;AAEA,MAAMS,UAAU,IAAI3D,IAGlB;IACA;QAAC;QAAW4B;KAAc;IAC1B;QAAC;QAASpB;KAAY;IACtB;QAAC;QAAcgB;KAAiB;IAChC;QAAC;QAAUD;KAAa;IACxB;QAAC;QAAUG;KAAa;CACzB;AAED,MAAMkC,sBAAsB;IAC1B;IACA;IACA;IACA;IACArD;CACD;AA+BD,SAASsD,gBACPvE,GAAoC;IAEpC,OAAO,AAACA,IAAsBwE,OAAO,KAAKvD;AAC5C;AAEA,SAASwD,kBACPzE,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKiB;AAC1C;AAEA,SAASyD,eAAe1E,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACduE,CAAAA,gBAAgBvE,QACfyE,kBAAkBzE,IAAmB;AAE3C;AA8CA,SAAS2E,UACP,KAAsC,EACtCvD,KAAyB,EACzBwD,MAAmB,EACnBC,KAAyB;IAHzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAKA,IAAIF,SAAUD,CAAAA,WAAW,UAAUA,WAAW,YAAW,GAAI;QAC3D,yDAAyD;QACzD,MAAMI,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAaxC,IAAI,CAAC2C,SAASF,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAavC,MAAM,EAAE;YACvB,MAAM2C,gBAAgBrB,KAAKsB,GAAG,IAAIL,gBAAgB;YAClD,OAAO;gBACLM,QAAQR,SAASS,MAAM,CAAC,CAACC,IAAMA,KAAKX,WAAW,CAAC,EAAE,GAAGO;gBACrDK,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQR;YAAUW,MAAM;QAAI;IACvC;IACA,IACE,OAAOtE,UAAU,YACjBwD,WAAW,UACXA,WAAW,cACX;QACA,OAAO;YAAEW,QAAQT;YAAaY,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAI/E,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACY;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACuE,GAAG,CACpC,CAACC,IAAMb,SAASc,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMb,QAAQ,CAACA,SAASrC,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAE6C;QAAQG,MAAM;IAAI;AAC7B;AAmBA,SAASK,iBAAiB,KASR;IATQ,IAAA,EACxB5E,MAAM,EACNnB,GAAG,EACHgG,WAAW,EACXpB,MAAM,EACNxD,KAAK,EACLC,OAAO,EACPwD,KAAK,EACLoB,MAAM,EACU,GATQ;IAUxB,IAAID,aAAa;QACf,OAAO;YAAEhG;YAAKkG,QAAQjF;YAAW4D,OAAO5D;QAAU;IACpD;IAEA,MAAM,EAAEsE,MAAM,EAAEG,IAAI,EAAE,GAAGf,UAAUxD,QAAQC,OAAOwD,QAAQC;IAC1D,MAAMsB,OAAOZ,OAAO7C,MAAM,GAAG;IAE7B,OAAO;QACLmC,OAAO,CAACA,SAASa,SAAS,MAAM,UAAUb;QAC1CqB,QAAQX,OACLI,GAAG,CACF,CAACC,GAAGQ,IACF,AAAGH,OAAO;gBAAE9E;gBAAQnB;gBAAKqB;gBAASD,OAAOwE;YAAE,KAAG,MAC5CF,CAAAA,SAAS,MAAME,IAAIQ,IAAI,CAAA,IACtBV,MAEN7D,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD7B,KAAKiG,OAAO;YAAE9E;YAAQnB;YAAKqB;YAASD,OAAOmE,MAAM,CAACY,KAAK;QAAC;IAC1D;AACF;AAEA,SAASE,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOlB,SAASkB,GAAG;IACrB;IACA,OAAOrF;AACT;AAEA,SAASsF,mBAAmBC,WAAuC;QAC/CA;IAAlB,MAAMC,YAAYD,EAAAA,sBAAAA,YAAYrF,MAAM,qBAAlBqF,oBAAoBP,MAAM,KAAI;IAChD,MAAMS,OAAOrC,QAAQvC,GAAG,CAAC2E;IACzB,IAAIC,MAAM;QACR,OAAOA,KAAKF;IACd;IACA,MAAM,IAAInE,MACR,AAAC,2DAAwD3C,cAAcmC,IAAI,CACzE,QACA,iBAAc4E;AAEpB;AAEA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASE,cACPC,GAA2B,EAC3B5G,GAAW,EACX4E,MAAmB,EACnBiC,WAA6B,EAC7BC,oBAA2E,EAC3EC,eAAqC;IAErC,IAAI,CAACH,OAAOA,IAAI5G,GAAG,KAAKY,gBAAgBgG,GAAG,CAAC,kBAAkB,KAAK5G,KAAK;QACtE;IACF;IACA4G,GAAG,CAAC,kBAAkB,GAAG5G;IACzB,MAAM8F,IAAI,YAAYc,MAAMA,IAAII,MAAM,KAAKC,QAAQC,OAAO;IAC1DpB,EAAEqB,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACR,IAAIS,UAAU,EAAE;YACnB,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACA9G,gBAAgB+G,GAAG,CAACtH;QACpB,IAAI6G,gBAAgB,QAAQ;YAC1BE,gBAAgB;QAClB;QACA,IAAID,wCAAAA,qBAAsBS,OAAO,EAAE;YACjC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGb;YACxC,mDAAmD;YACnD,sDAAsD;YACtDE,qBAAqBS,OAAO,CAAC;gBAAEC;gBAAcC;YAAc;QAC7D;QACA,IAAIrH,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;gBACrCqE;YAAJ,KAAIA,qBAAAA,IAAIc,aAAa,qBAAjBd,mBAAmBc,aAAa,EAAE;gBACpC,MAAMC,SAASC,iBAAiBhB,IAAIc,aAAa,CAACA,aAAa;gBAC/D,IAAI,CAACC,OAAOE,QAAQ,EAAE;gBACpB,sHAAsH;gBACxH,OAAO,IAAIjD,WAAW,gBAAgB+C,OAAOG,OAAO,KAAK,QAAQ;oBAC/DjI,SACE,AAAC,qBAAkBG,MAAI;gBAE3B,OAAO,IACL4E,WAAW,UACX+C,OAAOE,QAAQ,KAAK,cACpBF,OAAOE,QAAQ,KAAK,WACpBF,OAAOE,QAAQ,KAAK,YACpB;oBACAhI,SACE,AAAC,qBAAkBG,MAAI,6DAA0D2H,OAAOE,QAAQ,GAAC;gBAErG;YACF;QACF;IACF;AACF;AAEA,MAAME,eAAe;QAAC,EACpBC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVvD,MAAM,EACNwD,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,MAAM,EACN1B,WAAW,EACX2B,OAAO,EACPC,SAAS,EACTtH,MAAM,EACN6E,WAAW,EACXC,MAAM,EACNa,oBAAoB,EACpBC,eAAe,EACf2B,eAAe,EACfC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,aAAa,EACb,GAAGC,MACe;IAClBP,UAAUD,SAAS,SAASC;IAC5B,qBACE;;0BACE,KAAC5B;gBACE,GAAGmC,IAAI;gBACP,GAAGf,aAAa;gBACjBgB,UAAS;gBACTC,aAAWrE;gBACXwD,WAAWA;gBACXc,OAAO;oBAAE,GAAGb,QAAQ;oBAAE,GAAGC,SAAS;gBAAC;gBACnCa,KAAK/J,YACH,CAACwH;oBACC,IAAIxG,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;wBACzC,IAAIqE,OAAO,CAAC6B,WAAW;4BACrBnF,QAAQC,KAAK,CAAE,6CAA4CqD;wBAC7D;oBACF;oBACA8B,gBAAgB9B;oBAChB,IAAIA,uBAAAA,IAAKwC,QAAQ,EAAE;wBACjBzC,cACEC,KACA6B,WACA7D,QACAiC,aACAC,sBACAC;oBAEJ;gBACF,GACA;oBACE2B;oBACAD;oBACA7D;oBACAiC;oBACAC;oBACAC;iBACD;gBAEH4B,QAAQ,CAACU;oBACP,MAAMzC,MAAMyC,MAAMC,aAAa;oBAC/B3C,cACEC,KACA6B,WACA7D,QACAiC,aACAC,sBACAC;oBAEF,IAAI4B,QAAQ;wBACVA,OAAOU;oBACT;gBACF;gBACAT,SAAS,CAACS;oBACR,IAAIxC,gBAAgB,QAAQ;wBAC1B,2EAA2E;wBAC3EE,gBAAgB;oBAClB;oBACA,IAAI6B,SAAS;wBACXA,QAAQS;oBACV;gBACF;;YAEAd,CAAAA,UAAU1B,gBAAgB,MAAK,mBAC/B,KAAC0C;0BACC,cAAA,KAAC3C;oBACE,GAAGmC,IAAI;oBACR,kDAAkD;oBAClDP,SAASA;oBACTQ,UAAS;oBACTC,aAAWrE;oBACXsE,OAAOb;oBACPD,WAAWA;oBAIV,GAAGrC,iBAAiB;wBACnB5E;wBACAnB,KAAKyI;wBACLzC;wBACApB;wBACAxD,OAAO8G;wBACP7G,SAAS8G;wBACTtD,OAAOiE;wBACP7C;oBACF,EAAE;;;;;AAMd;AAEA,eAAe,SAASuD,MAAM,KAmBjB;IAnBiB,IAAA,EAC5BxJ,GAAG,EACH6E,KAAK,EACLmB,cAAc,KAAK,EACnByD,WAAW,KAAK,EAChBjB,OAAO,EACPkB,WAAW,IAAI,EACfC,YAAY,EACZvB,SAAS,EACT/G,OAAO,EACPD,KAAK,EACLwI,MAAM,EACNV,KAAK,EACLW,SAAS,EACTC,cAAc,EACdC,iBAAiB,EACjBlD,cAAc,OAAO,EACrBmD,WAAW,EACX,GAAGC,KACQ,GAnBiB;IAoB5B,MAAMC,gBAAgB7K,WAAWO;IACjC,MAAMuB,SAAsB7B,QAAQ;YAIhB6K;QAHlB,MAAMA,IAAIhK,aAAa+J,iBAAiBzK;QACxC,MAAMsF,WAAW;eAAIoF,EAAErF,WAAW;eAAKqF,EAAEC,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMzF,cAAcqF,EAAErF,WAAW,CAACuF,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,MAAM7G,aAAYyG,eAAAA,EAAEzG,SAAS,qBAAXyG,aAAaE,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QAClD,OAAO;YAAE,GAAGJ,CAAC;YAAEpF;YAAUD;YAAapB;QAAU;IAClD,GAAG;QAACwG;KAAc;IAElB,IAAInB,OAA4BkB;IAChC,IAAIrF,SAAmCC,QAAQ,eAAe;IAC9D,IAAI,YAAYkE,MAAM;QACpB,qDAAqD;QACrD,IAAIA,KAAKnE,MAAM,EAAEA,SAASmE,KAAKnE,MAAM;QAErC,+CAA+C;QAC/C,OAAOmE,KAAKnE,MAAM;IACpB;IAEA,IAAIqB,SAAgCM;IACpC,IAAI,YAAYwC,MAAM;QACpB,IAAIA,KAAK9C,MAAM,EAAE;YACf,MAAMuE,oBAAoBzB,KAAK9C,MAAM;YACrCA,SAAS,CAACwE;gBACR,MAAM,EAAEtJ,QAAQuJ,CAAC,EAAE,GAAGC,MAAM,GAAGF;gBAC/B,gDAAgD;gBAChD,2CAA2C;gBAC3C,OAAOD,kBAAkBG;YAC3B;QACF;QACA,8CAA8C;QAC9C,OAAO5B,KAAK9C,MAAM;IACpB;IAEA,IAAI2E,YAAY;IAChB,IAAIlG,eAAe1E,MAAM;QACvB,MAAM6K,kBAAkBtG,gBAAgBvE,OAAOA,IAAIwE,OAAO,GAAGxE;QAE7D,IAAI,CAAC6K,gBAAgB7K,GAAG,EAAE;YACxB,MAAM,IAAIqC,MACR,AAAC,gJAA6IM,KAAKC,SAAS,CAC1JiI;QAGN;QACAb,cAAcA,eAAea,gBAAgBb,WAAW;QACxDY,YAAYC,gBAAgB7K,GAAG;QAC/B,IAAI,CAAC4E,UAAUA,WAAW,QAAQ;YAChCgF,SAASA,UAAUiB,gBAAgBjB,MAAM;YACzCxI,QAAQA,SAASyJ,gBAAgBzJ,KAAK;YACtC,IAAI,CAACyJ,gBAAgBjB,MAAM,IAAI,CAACiB,gBAAgBzJ,KAAK,EAAE;gBACrD,MAAM,IAAIiB,MACR,AAAC,6JAA0JM,KAAKC,SAAS,CACvKiI;YAGN;QACF;IACF;IACA7K,MAAM,OAAOA,QAAQ,WAAWA,MAAM4K;IAEtC,IAAIrC,SACF,CAACkB,YAAajB,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAIxI,IAAI6C,UAAU,CAAC,YAAY7C,IAAI6C,UAAU,CAAC,UAAU;QACtD,uEAAuE;QACvEmD,cAAc;QACduC,SAAS;IACX;IACA,IAAI,OAAO1H,WAAW,eAAeN,gBAAgBuK,GAAG,CAAC9K,MAAM;QAC7DuI,SAAS;IACX;IACA,IAAIpH,OAAO6E,WAAW,EAAE;QACtBA,cAAc;IAChB;IAEA,MAAM,CAAC+E,cAAchE,gBAAgB,GAAGxH,SAAS;IACjD,MAAM,CAACmJ,iBAAiBsC,eAAeC,iBAAiB,GACtDtL,gBAAkC;QAChCuL,SAASxB;QACTyB,YAAYxB,gBAAgB;QAC5ByB,UAAU,CAAC7C;IACb;IACF,MAAMM,YAAY,CAACN,UAAUyC;IAE7B,MAAMK,eAAuD;QAC3DC,WAAW;QACXxD,SAAS;QACTyD,UAAU;QACVnK,OAAO;QACPwI,QAAQ;QACR4B,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,MAAMC,aAAqD;QACzDP,WAAW;QACXxD,SAAS;QACT1G,OAAO;QACPwI,QAAQ;QACR4B,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,IAAIE,WAAW;IACf,IAAIC;IACJ,MAAMC,cAA+B;QACnCnE,UAAU;QACVoE,KAAK;QACLC,MAAM;QACNC,QAAQ;QACRC,OAAO;QAEPd,WAAW;QACXM,SAAS;QACTF,QAAQ;QACRC,QAAQ;QAER7D,SAAS;QACT1G,OAAO;QACPwI,QAAQ;QACRyC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,WAAW;QAEX3C;QACAC;IACF;IAEA,IAAI5B,WAAW7B,OAAOjF;IACtB,IAAI6G,YAAY5B,OAAOuD;IACvB,MAAMzB,aAAa9B,OAAOhF;IAE1B,IAAIjB,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,CAACvC,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CkI,WAAWA,YAAY;YACvBD,YAAYA,aAAa;YACzBjC,cAAc;QAChB,OAAO;YACL,IAAI,CAAC1B,oBAAoBX,QAAQ,CAACiB,SAAS;gBACzC,MAAM,IAAIvC,MACR,AAAC,qBAAkBrC,MAAI,gDAA6C4E,SAAO,wBAAqBN,oBAAoBqB,GAAG,CACrH8G,QACA5K,IAAI,CAAC,OAAK;YAEhB;YAEA,IACE,AAAC,OAAOqG,aAAa,eAAewE,MAAMxE,aACzC,OAAOD,cAAc,eAAeyE,MAAMzE,YAC3C;gBACA,MAAM,IAAI5F,MACR,AAAC,qBAAkBrC,MAAI;YAE3B;YACA,IAAI4E,WAAW,UAAWxD,CAAAA,SAASwI,MAAK,GAAI;gBAC1C/J,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YACA,IAAI,CAACgB,qBAAqB2C,QAAQ,CAAC6E,UAAU;gBAC3C,MAAM,IAAInG,MACR,AAAC,qBAAkBrC,MAAI,iDAA8CwI,UAAQ,wBAAqBxH,qBAAqB2E,GAAG,CACxH8G,QACA5K,IAAI,CAAC,OAAK;YAEhB;YACA,IAAI4H,YAAYjB,YAAY,QAAQ;gBAClC,MAAM,IAAInG,MACR,AAAC,qBAAkBrC,MAAI;YAE3B;YACA,IAAI6E,SAASD,WAAW,UAAUA,WAAW,cAAc;gBACzD/E,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YACA,IAAI6G,gBAAgB,QAAQ;gBAC1B,IAAIjC,WAAW,UAAU,AAACsD,CAAAA,YAAY,CAAA,IAAMD,CAAAA,aAAa,CAAA,IAAK,MAAM;oBAClEpI,SACE,AAAC,qBAAkBG,MAAI;gBAE3B;gBACA,IAAI,CAACgK,aAAa;oBAChB,MAAM2C,iBAAiB;wBAAC;wBAAQ;wBAAO;wBAAQ;qBAAO,CAAC,iCAAiC;;oBAExF,MAAM,IAAItK,MACR,AAAC,qBAAkBrC,MAAI,mUAGgE2M,eAAe9K,IAAI,CACxG,OACA;gBAIN;YACF;YACA,IAAI,SAASkH,MAAM;gBACjBlJ,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YAEA,IAAI,CAACgG,eAAeC,WAAWM,oBAAoB;gBACjD,MAAMqG,SAAS3G,OAAO;oBACpB9E;oBACAnB;oBACAoB,OAAO8G,YAAY;oBACnB7G,SAAS8G,cAAc;gBACzB;gBACA,IAAI7G;gBACJ,IAAI;oBACFA,MAAM,IAAIC,IAAIqL;gBAChB,EAAE,OAAOvJ,KAAK,CAAC;gBACf,IAAIuJ,WAAW5M,OAAQsB,OAAOA,IAAIuL,QAAQ,KAAK7M,OAAO,CAACsB,IAAIwL,MAAM,EAAG;oBAClEjN,SACE,AAAC,qBAAkBG,MAAI,4HACpB;gBAEP;YACF;YAEA,IAAIkJ,OAAO;gBACT,IAAI6D,oBAAoBC,OAAOC,IAAI,CAAC/D,OAAO1D,MAAM,CAC/C,CAAC0H,MAAQA,OAAOlB;gBAElB,IAAIe,kBAAkBrK,MAAM,EAAE;oBAC5B7C,SACE,AAAC,oBAAiBG,MAAI,iGAA8F+M,kBAAkBlL,IAAI,CACxI;gBAGN;YACF;YAEA,IACE,OAAOhB,WAAW,eAClB,CAACF,gBACDE,OAAOsM,mBAAmB,EAC1B;gBACAxM,eAAe,IAAIwM,oBAAoB,CAACC;oBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;4BAE3BD;wBADf,0EAA0E;wBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgBrN,GAAG,KAAI;wBACtC,MAAMyN,WAAWhN,QAAQqB,GAAG,CAACyL;wBAC7B,IACEE,YACA,CAACA,SAAShE,QAAQ,IAClBgE,SAAS5G,WAAW,KAAK,UACzB,CAAC4G,SAASzN,GAAG,CAAC6C,UAAU,CAAC,YACzB,CAAC4K,SAASzN,GAAG,CAAC6C,UAAU,CAAC,UACzB;4BACA,iDAAiD;4BACjDhD,SACE,AAAC,qBAAkB4N,SAASzN,GAAG,GAAC,8HAC7B;wBAEP;oBACF;gBACF;gBACA,IAAI;oBACFW,aAAa+M,OAAO,CAAC;wBACnBC,MAAM;wBACNC,UAAU;oBACZ;gBACF,EAAE,OAAOvK,KAAK;oBACZ,oCAAoC;oBACpCC,QAAQC,KAAK,CAACF;gBAChB;YACF;QACF;IACF;IACA,MAAMgF,WAAW2E,OAAOa,MAAM,CAAC,CAAC,GAAG3E,OAAO8C;IAC1C,MAAM1D,YACJzB,gBAAgB,UAAU,CAACkE,eACvB;QACE+C,gBAAgBjE,aAAa;QAC7BkE,oBAAoBjE,kBAAkB;QACtCtE,QAAQ;QACRwI,iBAAiB,AAAC,UAAOhE,cAAY;IACvC,IACA,CAAC;IACP,IAAIpF,WAAW,QAAQ;QACrB,sCAAsC;QACtCyG,aAAavD,OAAO,GAAG;QACvBuD,aAAaxD,QAAQ,GAAG;QACxBwD,aAAaY,GAAG,GAAG;QACnBZ,aAAaa,IAAI,GAAG;QACpBb,aAAac,MAAM,GAAG;QACtBd,aAAae,KAAK,GAAG;IACvB,OAAO,IACL,OAAOlE,aAAa,eACpB,OAAOD,cAAc,aACrB;QACA,iDAAiD;QACjD,MAAMgG,WAAWhG,YAAYC;QAC7B,MAAMgG,aAAaxB,MAAMuB,YAAY,SAAS,AAAC,KAAEA,WAAW,MAAI;QAChE,IAAIrJ,WAAW,cAAc;YAC3B,qEAAqE;YACrEyG,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBiE,WAAW;YACXD,WAAWqC,UAAU,GAAGA;QAC1B,OAAO,IAAItJ,WAAW,aAAa;YACjC,oEAAoE;YACpEyG,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBwD,aAAaiB,QAAQ,GAAG;YACxBR,WAAW;YACXD,WAAWS,QAAQ,GAAG;YACtBP,cAAc,AAAC,uGAAoG7D,WAAS,qBAAkBD,YAAU;QAC1J,OAAO,IAAIrD,WAAW,SAAS;YAC7B,gEAAgE;YAChEyG,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBwD,aAAajK,KAAK,GAAG8G;YACrBmD,aAAazB,MAAM,GAAG3B;QACxB;IACF,OAAO;QACL,wBAAwB;QACxB,IAAI7H,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;YACzC,MAAM,IAAIF,MACR,AAAC,qBAAkBrC,MAAI;QAE3B;IACF;IAEA,IAAIgI,gBAAmC;QACrChI,KAAKY;QACLsF,QAAQjF;QACR4D,OAAO5D;IACT;IAEA,IAAI4H,WAAW;QACbb,gBAAgBjC,iBAAiB;YAC/B5E;YACAnB;YACAgG;YACApB;YACAxD,OAAO8G;YACP7G,SAAS8G;YACTtD;YACAoB;QACF;IACF;IAEA,IAAIwC,YAAoBzI;IAExB,IAAII,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAO1B,WAAW,aAAa;YACjC,IAAIsN;YACJ,IAAI;gBACFA,UAAU,IAAI5M,IAAIyG,cAAchI,GAAG;YACrC,EAAE,OAAOoO,GAAG;gBACVD,UAAU,IAAI5M,IAAIyG,cAAchI,GAAG,EAAEa,OAAOwN,QAAQ,CAACrM,IAAI;YAC3D;YACAvB,QAAQkB,GAAG,CAACwM,QAAQnM,IAAI,EAAE;gBAAEhC;gBAAKyJ;gBAAU5C;YAAY;QACzD;IACF;IAEA,MAAMyH,YAGF;QACFC,aAAavG,cAAc9B,MAAM;QACjCkE,YAAYpC,cAAcnD,KAAK;QAC/B2J,aAAazF,KAAKyF,WAAW;QAC7BC,gBAAgB1F,KAAK0F,cAAc;IACrC;IAEA,MAAMC,kBACJ,OAAO7N,WAAW,cAAc5B,MAAME,SAAS,GAAGF,MAAMyP,eAAe;IACzE,MAAM5H,uBAAuB5H,OAAO6K;IAEpC,MAAM4E,mBAAmBzP,OAA8Bc;IACvDb,UAAU;QACR2H,qBAAqBS,OAAO,GAAGwC;IACjC,GAAG;QAACA;KAAkB;IAEtB2E,gBAAgB;QACd,IAAIC,iBAAiBpH,OAAO,KAAKvH,KAAK;YACpCiL;YACA0D,iBAAiBpH,OAAO,GAAGvH;QAC7B;IACF,GAAG;QAACiL;QAAkBjL;KAAI;IAE1B,MAAM4O,iBAAiB;QACrBrG;QACAP;QACAC;QACAC;QACAC;QACAvD;QACAwD;QACAC;QACAC;QACAE;QACArH;QACA6E;QACAa;QACAZ;QACAwC;QACA3B;QACAC;QACA2B;QACAG;QACAC,eAAejE;QACf,GAAGkE,IAAI;IACT;IACA,qBACE;;0BACE,MAAC8F;gBAAK3F,OAAOmC;;oBACVS,yBACC,KAAC+C;wBAAK3F,OAAO2C;kCACVE,4BACC,KAACnF;4BACCsC,OAAO;gCACLpB,SAAS;gCACTwE,UAAU;gCACVlL,OAAO;gCACPwI,QAAQ;gCACR4B,YAAY;gCACZC,SAAS;gCACTC,QAAQ;gCACRC,QAAQ;gCACRC,SAAS;4BACX;4BACAkD,KAAI;4BACJC,eAAa;4BACb/O,KAAK+L;6BAEL;yBAEJ;kCACJ,KAAChE;wBAAc,GAAG6G,cAAc;;;;YAEjCnF,WACC,sEAAsE;YACtE,qEAAqE;YACrE,6DAA6D;YAC7D,EAAE;YACF,8EAA8E;0BAC9E,KAACjK;0BACC,cAAA,KAACwP;oBAOCC,KAAI;oBACJC,IAAG;oBACHlN,MAAMgG,cAAc9B,MAAM,GAAGjF,YAAY+G,cAAchI,GAAG;oBACzD,GAAGsO,SAAS;mBARX,YACAtG,cAAchI,GAAG,GACjBgI,cAAc9B,MAAM,GACpB8B,cAAcnD,KAAK;iBAQvB;;;AAGV"}