{"version": 3, "sources": ["../../../src/client/dev/on-demand-entries-client.ts"], "names": ["Router", "sendMessage", "page", "setInterval", "JSON", "stringify", "event", "ready", "notFoundSrcPage", "self", "__NEXT_DATA__", "pathname"], "mappings": "AAAA,OAAOA,YAAY,YAAW;AAC9B,SAASC,WAAW,QAAQ,kDAAiD;AAE7E,eAAe,CAAA,OAAOC;IACpB,IAAIA,MAAM;QACR,wDAAwD;QACxD,sDAAsD;QACtDC,YAAY;YACVF,YAAYG,KAAKC,SAAS,CAAC;gBAAEC,OAAO;gBAAQJ;YAAK;QACnD,GAAG;IACL,OAAO;QACLF,OAAOO,KAAK,CAAC;YACXJ,YAAY;gBACV,iEAAiE;gBACjE,gEAAgE;gBAChE,+DAA+D;gBAC/D,MAAMK,kBAAkBC,KAAKC,aAAa,CAACF,eAAe;gBAC1D,MAAMG,WACJ,AAACX,CAAAA,OAAOW,QAAQ,KAAK,UAAUX,OAAOW,QAAQ,KAAK,SAAQ,KAC3DH,kBACIA,kBACAR,OAAOW,QAAQ;gBAErBV,YAAYG,KAAKC,SAAS,CAAC;oBAAEC,OAAO;oBAAQJ,MAAMS;gBAAS;YAC7D,GAAG;QACL;IACF;AACF,CAAA,EAAC"}