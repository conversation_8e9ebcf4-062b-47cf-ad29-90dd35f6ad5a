{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts"], "names": ["createRouterCache<PERSON>ey", "PrefetchCacheEntryStatus", "fillLazyItemsTillLeafWithHead", "newCache", "existingCache", "routerState", "cacheNodeSeedData", "head", "prefetchEntry", "isLastSegment", "Object", "keys", "length", "key", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "parallelSeedData", "undefined", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "hasReusablePrefetch", "kind", "status", "reusable", "parallelRouteCacheNode", "Map", "existingCacheNode", "newCacheNode", "seedNode", "loading", "lazyData", "rsc", "prefetchRsc", "prefetchHead", "lazyDataResolved", "set", "existingParallelRoutes"], "mappings": "AAKA,SAASA,oBAAoB,QAAQ,4BAA2B;AAChE,SACEC,wBAAwB,QAEnB,yBAAwB;AAE/B,OAAO,SAASC,8BACdC,QAAmB,EACnBC,aAAoC,EACpCC,WAA8B,EAC9BC,iBAA2C,EAC3CC,IAAqB,EACrBC,aAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,IAAI,CAACN,WAAW,CAAC,EAAE,EAAEO,MAAM,KAAK;IAC7D,IAAIH,eAAe;QACjBN,SAASI,IAAI,GAAGA;QAChB;IACF;IACA,uFAAuF;IACvF,IAAK,MAAMM,OAAOR,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMS,qBAAqBT,WAAW,CAAC,EAAE,CAACQ,IAAI;QAC9C,MAAME,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAWhB,qBAAqBe;QAEtC,4EAA4E;QAC5E,2EAA2E;QAC3E,wEAAwE;QACxE,wEAAwE;QACxE,0EAA0E;QAC1E,qBAAqB;QACrB,EAAE;QACF,0EAA0E;QAC1E,wEAAwE;QACxE,kEAAkE;QAClE,MAAME,mBACJX,sBAAsB,QAAQA,iBAAiB,CAAC,EAAE,CAACO,IAAI,KAAKK,YACxDZ,iBAAiB,CAAC,EAAE,CAACO,IAAI,GACzB;QACN,IAAIT,eAAe;YACjB,MAAMe,kCACJf,cAAcgB,cAAc,CAACC,GAAG,CAACR;YACnC,IAAIM,iCAAiC;gBACnC,MAAMG,sBACJd,CAAAA,iCAAAA,cAAee,IAAI,MAAK,UACxBf,cAAcgB,MAAM,KAAKvB,yBAAyBwB,QAAQ;gBAE5D,IAAIC,yBAAyB,IAAIC,IAAIR;gBACrC,MAAMS,oBAAoBF,uBAAuBL,GAAG,CAACL;gBACrD,IAAIa;gBACJ,IAAIZ,qBAAqB,MAAM;oBAC7B,qCAAqC;oBACrC,MAAMa,WAAWb,gBAAgB,CAAC,EAAE;oBACpC,MAAMc,UAAUd,gBAAgB,CAAC,EAAE;oBACnCY,eAAe;wBACbG,UAAU;wBACVC,KAAKH;wBACL,kEAAkE;wBAClE,oEAAoE;wBACpE,2DAA2D;wBAC3D,kEAAkE;wBAClE,+BAA+B;wBAC/BI,aAAa;wBACb3B,MAAM;wBACN4B,cAAc;wBACdJ;wBACAX,gBAAgB,IAAIO,IAAIC,qCAAAA,kBAAmBR,cAAc;wBACzDgB,kBAAkB;oBACpB;gBACF,OAAO,IAAId,uBAAuBM,mBAAmB;oBACnD,oEAAoE;oBACpE,2CAA2C;oBAC3CC,eAAe;wBACbG,UAAUJ,kBAAkBI,QAAQ;wBACpCC,KAAKL,kBAAkBK,GAAG;wBAC1B,oEAAoE;wBACpE,kEAAkE;wBAClE,2BAA2B;wBAC3BC,aAAaN,kBAAkBM,WAAW;wBAC1C3B,MAAMqB,kBAAkBrB,IAAI;wBAC5B4B,cAAcP,kBAAkBO,YAAY;wBAC5Cf,gBAAgB,IAAIO,IAAIC,kBAAkBR,cAAc;wBACxDgB,kBAAkBR,kBAAkBQ,gBAAgB;wBACpDL,SAASH,kBAAkBG,OAAO;oBACpC;gBACF,OAAO;oBACL,kEAAkE;oBAClE,iBAAiB;oBACjBF,eAAe;wBACbG,UAAU;wBACVC,KAAK;wBACLC,aAAa;wBACb3B,MAAM;wBACN4B,cAAc;wBACdf,gBAAgB,IAAIO,IAAIC,qCAAAA,kBAAmBR,cAAc;wBACzDgB,kBAAkB;wBAClBL,SAAS;oBACX;gBACF;gBAEA,mDAAmD;gBACnDL,uBAAuBW,GAAG,CAACrB,UAAUa;gBACrC,qEAAqE;gBACrE3B,8BACE2B,cACAD,mBACAd,oBACAG,mBAAmBA,mBAAmB,MACtCV,MACAC;gBAGFL,SAASiB,cAAc,CAACiB,GAAG,CAACxB,KAAKa;gBACjC;YACF;QACF;QAEA,IAAIG;QACJ,IAAIZ,qBAAqB,MAAM;YAC7B,qCAAqC;YACrC,MAAMa,WAAWb,gBAAgB,CAAC,EAAE;YACpC,MAAMc,UAAUd,gBAAgB,CAAC,EAAE;YACnCY,eAAe;gBACbG,UAAU;gBACVC,KAAKH;gBACLI,aAAa;gBACb3B,MAAM;gBACN4B,cAAc;gBACdf,gBAAgB,IAAIO;gBACpBS,kBAAkB;gBAClBL;YACF;QACF,OAAO;YACL,kEAAkE;YAClE,iBAAiB;YACjBF,eAAe;gBACbG,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACb3B,MAAM;gBACN4B,cAAc;gBACdf,gBAAgB,IAAIO;gBACpBS,kBAAkB;gBAClBL,SAAS;YACX;QACF;QAEA,MAAMO,yBAAyBnC,SAASiB,cAAc,CAACC,GAAG,CAACR;QAC3D,IAAIyB,wBAAwB;YAC1BA,uBAAuBD,GAAG,CAACrB,UAAUa;QACvC,OAAO;YACL1B,SAASiB,cAAc,CAACiB,GAAG,CAACxB,KAAK,IAAIc,IAAI;gBAAC;oBAACX;oBAAUa;iBAAa;aAAC;QACrE;QAEA3B,8BACE2B,cACAX,WACAJ,oBACAG,kBACAV,MACAC;IAEJ;AACF"}