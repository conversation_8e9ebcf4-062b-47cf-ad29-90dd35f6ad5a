{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fetch-server-response.ts"], "names": ["createFromFetch", "process", "env", "NEXT_RUNTIME", "require", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_DID_POSTPONE_HEADER", "urlToUrlWithoutFlightMarker", "callServer", "PrefetchKind", "hexHash", "doMpaNavigation", "url", "toString", "undefined", "fetchServerResponse", "flightRouterState", "nextUrl", "currentBuildId", "prefetchKind", "headers", "encodeURIComponent", "JSON", "stringify", "AUTO", "NEXT_DEPLOYMENT_ID", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "res", "fetchUrl", "URL", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "searchParams", "set", "fetch", "credentials", "responseUrl", "canonicalUrl", "redirected", "contentType", "get", "postponed", "interception", "includes", "isFlightResponse", "startsWith", "ok", "hash", "buildId", "flightData", "Promise", "resolve", "err", "console", "error"], "mappings": "AAAA;AAEA,aAAa;AACb,6DAA6D;AAC7D,oEAAoE;AACpE,MAAM,EAAEA,eAAe,EAAE,GACvB,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAQd,SACEC,2BAA2B,EAC3BC,sBAAsB,EACtBC,oBAAoB,EACpBC,QAAQ,EACRC,UAAU,EACVC,uBAAuB,EACvBC,wBAAwB,QACnB,wBAAuB;AAC9B,SAASC,2BAA2B,QAAQ,gBAAe;AAC3D,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,YAAY,QAAQ,yBAAwB;AACrD,SAASC,OAAO,QAAQ,2BAA0B;AASlD,SAASC,gBAAgBC,GAAW;IAClC,OAAO;QAACL,4BAA4BK,KAAKC,QAAQ;QAAIC;QAAW;QAAO;KAAM;AAC/E;AAEA;;CAEC,GACD,OAAO,eAAeC,oBACpBH,GAAQ,EACRI,iBAAoC,EACpCC,OAAsB,EACtBC,cAAsB,EACtBC,YAA2B;IAE3B,MAAMC,UAMF;QACF,yBAAyB;QACzB,CAAChB,WAAW,EAAE;QACd,mCAAmC;QACnC,CAACH,uBAAuB,EAAEoB,mBACxBC,KAAKC,SAAS,CAACP;IAEnB;IAEA;;;;;GAKC,GACD,IAAIG,iBAAiBV,aAAae,IAAI,EAAE;QACtCJ,OAAO,CAACpB,4BAA4B,GAAG;IACzC;IAEA,IAAIiB,SAAS;QACXG,OAAO,CAACjB,SAAS,GAAGc;IACtB;IAEA,IAAIrB,QAAQC,GAAG,CAAC4B,kBAAkB,EAAE;QAClCL,OAAO,CAAC,kBAAkB,GAAGxB,QAAQC,GAAG,CAAC4B,kBAAkB;IAC7D;IAEA,MAAMC,mBAAmBhB,QACvB;QACEU,OAAO,CAACpB,4BAA4B,IAAI;QACxCoB,OAAO,CAACnB,uBAAuB;QAC/BmB,OAAO,CAACjB,SAAS;KAClB,CAACwB,IAAI,CAAC;IAGT,IAAI;YA0BqBC;QAzBvB,IAAIC,WAAW,IAAIC,IAAIlB;QACvB,IAAIhB,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;YACzC,IAAInC,QAAQC,GAAG,CAACmC,oBAAoB,KAAK,UAAU;gBACjD,IAAIH,SAASI,QAAQ,CAACC,QAAQ,CAAC,MAAM;oBACnCL,SAASI,QAAQ,IAAI;gBACvB,OAAO;oBACLJ,SAASI,QAAQ,IAAI;gBACvB;YACF;QACF;QAEA,8FAA8F;QAC9FJ,SAASM,YAAY,CAACC,GAAG,CAAClC,sBAAsBwB;QAEhD,MAAME,MAAM,MAAMS,MAAMR,UAAU;YAChC,wFAAwF;YACxFS,aAAa;YACblB;QACF;QAEA,MAAMmB,cAAchC,4BAA4BqB,IAAIhB,GAAG;QACvD,MAAM4B,eAAeZ,IAAIa,UAAU,GAAGF,cAAczB;QAEpD,MAAM4B,cAAcd,IAAIR,OAAO,CAACuB,GAAG,CAAC,mBAAmB;QACvD,MAAMC,YAAY,CAAC,CAAChB,IAAIR,OAAO,CAACuB,GAAG,CAACrC;QACpC,MAAMuC,eAAe,CAAC,GAACjB,mBAAAA,IAAIR,OAAO,CAACuB,GAAG,CAAC,4BAAhBf,iBAAyBkB,QAAQ,CAAC3C;QACzD,IAAI4C,mBAAmBL,gBAAgBrC;QAEvC,IAAIT,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;YACzC,IAAInC,QAAQC,GAAG,CAACmC,oBAAoB,KAAK,UAAU;gBACjD,IAAI,CAACe,kBAAkB;oBACrBA,mBAAmBL,YAAYM,UAAU,CAAC;gBAC5C;YACF;QACF;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACD,oBAAoB,CAACnB,IAAIqB,EAAE,EAAE;YAChC,2FAA2F;YAC3F,IAAIrC,IAAIsC,IAAI,EAAE;gBACZX,YAAYW,IAAI,GAAGtC,IAAIsC,IAAI;YAC7B;YAEA,OAAOvC,gBAAgB4B,YAAY1B,QAAQ;QAC7C;QAEA,2EAA2E;QAC3E,MAAM,CAACsC,SAASC,WAAW,GAAuB,MAAMzD,gBACtD0D,QAAQC,OAAO,CAAC1B,MAChB;YACEpB;QACF;QAGF,IAAIU,mBAAmBiC,SAAS;YAC9B,OAAOxC,gBAAgBiB,IAAIhB,GAAG;QAChC;QAEA,OAAO;YAACwC;YAAYZ;YAAcI;YAAWC;SAAa;IAC5D,EAAE,OAAOU,KAAK;QACZC,QAAQC,KAAK,CACX,AAAC,qCAAkC7C,MAAI,yCACvC2C;QAEF,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YAAC3C,IAAIC,QAAQ;YAAIC;YAAW;YAAO;SAAM;IAClD;AACF"}