{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["callServer", "ACTION", "NEXT_ROUTER_STATE_TREE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "addBasePath", "createHrefFromUrl", "handleExternalUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleMutable", "fillLazyItemsTillLeafWithHead", "createEmptyCacheNode", "hasInterceptionRouteInCurrentTree", "handleSegmentMismatch", "refreshInactiveParallelSegments", "fetchServerAction", "state", "nextUrl", "actionId", "actionArgs", "body", "res", "fetch", "method", "headers", "Accept", "encodeURIComponent", "JSON", "stringify", "tree", "NEXT_DEPLOYMENT_ID", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "actionFlightData", "actionResult", "serverActionReducer", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "inFlightServerAction", "then", "flightData", "pushRef", "pendingPush", "newHref", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "cacheNodeSeedData", "head", "slice", "rsc", "cache", "prefetchRsc", "updatedTree", "updatedCache", "includeNextUrl", "Boolean", "prefetchCache", "Map", "patchedTree"], "mappings": "AAKA,SAASA,UAAU,QAAQ,2BAA0B;AACrD,SACEC,MAAM,EACNC,sBAAsB,EACtBC,QAAQ,EACRC,uBAAuB,QAClB,2BAA0B;AACjC,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AASd,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAEjF,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,qBAAqB,QAAQ,6BAA4B;AAClE,SAASC,+BAA+B,QAAQ,wCAAuC;AAavF,eAAeC,kBACbC,KAA2B,EAC3BC,OAAwC,EACxC,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMrB,YAAYoB;IAE/B,MAAME,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQ5B;YACR,CAACH,OAAO,EAAEwB;YACV,CAACvB,uBAAuB,EAAE+B,mBAAmBC,KAAKC,SAAS,CAACZ,MAAMa,IAAI;YACtE,GAAI7B,QAAQC,GAAG,CAAC6B,kBAAkB,GAC9B;gBACE,mBAAmB9B,QAAQC,GAAG,CAAC6B,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAIb,UACA;gBACE,CAACrB,SAAS,EAAEqB;YACd,IACA,CAAC,CAAC;QACR;QACAG;IACF;IAEA,MAAMW,WAAWV,IAAIG,OAAO,CAACQ,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBP,KAAKQ,KAAK,CAClCd,IAAIG,OAAO,CAACQ,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFrC,YAAY2B,WACZ,sFAAsF;IACtF,IAAIU,IAAIzB,MAAM0B,YAAY,EAAEC,OAAOZ,QAAQ,CAACa,IAAI,KAElDC;IAEJ,IAAIC,mBACFzB,IAAIG,OAAO,CAACQ,GAAG,CAAC,oBAAoBnC;IAEtC,IAAIiD,kBAAkB;QACpB,MAAMC,WAAiC,MAAMjD,gBAC3CkD,QAAQC,OAAO,CAAC5B,MAChB;YACE5B;QACF;QAGF,IAAIsC,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGmB,iBAAiB,GAAG,AAACH,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLG,kBAAkBA;gBAClBV;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACkB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACH,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLI;YACAD;YACAV;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,SAASmB,oBACdpC,KAA2B,EAC3BqC,MAA0B;IAE1B,MAAM,EAAEJ,OAAO,EAAEK,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IACtC,MAAMX,OAAO5B,MAAM0B,YAAY;IAE/B,IAAIc,cAAcxC,MAAMa,IAAI;IAE5B0B,QAAQE,0BAA0B,GAAG;IAErC,2GAA2G;IAC3G,mEAAmE;IACnE,4EAA4E;IAC5E,wDAAwD;IACxD,MAAMxC,UACJD,MAAMC,OAAO,IAAIL,kCAAkCI,MAAMa,IAAI,IACzDb,MAAMC,OAAO,GACb;IAENsC,QAAQG,oBAAoB,GAAG3C,kBAAkBC,OAAOC,SAASoC;IAEjE,OAAOE,QAAQG,oBAAoB,CAACC,IAAI,CACtC;YAAO,EACLR,YAAY,EACZD,kBAAkBU,UAAU,EAC5BpB,gBAAgB,EACjB;QACC,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIA,kBAAkB;YACpBxB,MAAM6C,OAAO,CAACC,WAAW,GAAG;YAC5BP,QAAQO,WAAW,GAAG;QACxB;QAEA,IAAI,CAACF,YAAY;YACfX,QAAQE;YAER,2EAA2E;YAC3E,IAAIX,kBAAkB;gBACpB,OAAOlC,kBACLU,OACAuC,SACAf,iBAAiBI,IAAI,EACrB5B,MAAM6C,OAAO,CAACC,WAAW;YAE7B;YACA,OAAO9C;QACT;QAEA,IAAI,OAAO4C,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAOtD,kBACLU,OACAuC,SACAK,YACA5C,MAAM6C,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DP,QAAQG,oBAAoB,GAAG;QAE/B,IAAIlB,kBAAkB;YACpB,MAAMuB,UAAU1D,kBAAkBmC,kBAAkB;YACpDe,QAAQb,YAAY,GAAGqB;QACzB;QAEA,KAAK,MAAMC,kBAAkBJ,WAAY;YACvC,oFAAoF;YACpF,IAAII,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOnD;YACT;YAEA,mGAAmG;YACnG,MAAM,CAACoD,UAAU,GAAGJ;YACpB,MAAMK,UAAU9D,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJiD,aACAY,WACA5B,mBACInC,kBAAkBmC,oBAClBxB,MAAM0B,YAAY;YAGxB,IAAI2B,YAAY,MAAM;gBACpB,OAAOxD,sBAAsBG,OAAOqC,QAAQe;YAC9C;YAEA,IAAI5D,4BAA4BgD,aAAaa,UAAU;gBACrD,OAAO/D,kBACLU,OACAuC,SACAX,MACA5B,MAAM6C,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACQ,mBAAmBC,KAAK,GAAGP,eAAeQ,KAAK,CAAC,CAAC;YACxD,MAAMC,MAAMH,sBAAsB,OAAOA,iBAAiB,CAAC,EAAE,GAAG;YAEhE,8FAA8F;YAC9F,IAAIG,QAAQ,MAAM;gBAChB,MAAMC,QAAmB/D;gBACzB+D,MAAMD,GAAG,GAAGA;gBACZC,MAAMC,WAAW,GAAG;gBACpBjE,8BACEgE,OACA,4FAA4F;gBAC5F7B,WACAuB,WACAE,mBACAC;gBAGF,MAAMzD,gCAAgC;oBACpCE;oBACA4D,aAAaP;oBACbQ,cAAcH;oBACdI,gBAAgBC,QAAQ9D;oBACxByB,cAAca,QAAQb,YAAY,IAAI1B,MAAM0B,YAAY;gBAC1D;gBAEAa,QAAQmB,KAAK,GAAGA;gBAChBnB,QAAQyB,aAAa,GAAG,IAAIC;YAC9B;YAEA1B,QAAQ2B,WAAW,GAAGb;YACtBb,cAAca;QAChB;QAEApB,QAAQE;QAER,OAAO1C,cAAcO,OAAOuC;IAC9B,GACA,CAAChB;QACC,mHAAmH;QACnHe,OAAOf;QAEP,OAAOvB;IACT;AAEJ"}