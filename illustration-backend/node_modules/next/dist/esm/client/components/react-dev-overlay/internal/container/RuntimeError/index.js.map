{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/index.tsx"], "names": ["React", "CodeFrame", "noop", "css", "groupStackFramesByFramework", "GroupedStackFrames", "RuntimeError", "error", "firstFrame", "allLeadingFrames", "allCallStackFrames", "useMemo", "filteredFrames", "frames", "filter", "f", "sourceStackFrame", "file", "includes", "methodName", "startsWith", "firstFirstPartyFrameIndex", "findIndex", "entry", "expanded", "Boolean", "originalCodeFrame", "originalStackFrame", "slice", "all", "setAll", "useState", "canShowMore", "leadingFramesGroupedByFramework", "stackFramesGroupedByFramework", "leading<PERSON>ram<PERSON>", "visibleCallStackFrames", "length", "Fragment", "h2", "groupedStackFrames", "show", "stackFrame", "codeFrame", "undefined", "button", "tabIndex", "data-nextjs-data-runtime-error-collapsed-action", "type", "onClick", "styles"], "mappings": ";;;;;;;;;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,SAAS,QAAQ,6BAA4B;AAEtD,SAASC,QAAQC,GAAG,QAAQ,8BAA6B;AACzD,SAASC,2BAA2B,QAAQ,gDAA+C;AAC3F,SAASC,kBAAkB,QAAQ,uBAAsB;AAIzD,OAAO,SAASC,aAAa,KAA4B;IAA5B,IAAA,EAAEC,KAAK,EAAqB,GAA5B;IAC3B,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAE,GACxDV,MAAMW,OAAO,CAAC;QACZ,MAAMC,iBAAiBL,MAAMM,MAAM,CAACC,MAAM,CACxC,CAACC;gBAIOA;mBAHN,CACEA,CAAAA,EAAEC,gBAAgB,CAACC,IAAI,KAAK,iBAC5B;gBAAC;gBAAa;aAAY,CAACC,QAAQ,CAACH,EAAEC,gBAAgB,CAACG,UAAU,CAAA,KAC9D,GAACJ,2BAAAA,EAAEC,gBAAgB,CAACC,IAAI,qBAAvBF,yBAAyBK,UAAU,CAAC;;QAG9C,MAAMC,4BAA4BT,eAAeU,SAAS,CACxD,CAACC,QACCA,MAAMC,QAAQ,IACdC,QAAQF,MAAMG,iBAAiB,KAC/BD,QAAQF,MAAMI,kBAAkB;YAItBf;QADd,OAAO;YACLJ,YAAYI,CAAAA,4CAAAA,cAAc,CAACS,0BAA0B,YAAzCT,4CAA6C;YACzDH,kBACEY,4BAA4B,IACxB,EAAE,GACFT,eAAegB,KAAK,CAAC,GAAGP;YAC9BX,oBAAoBE,eAAegB,KAAK,CAACP,4BAA4B;QACvE;IACF,GAAG;QAACd,MAAMM,MAAM;KAAC;IAEnB,MAAM,CAACgB,KAAKC,OAAO,GAAG9B,MAAM+B,QAAQ,CAACvB,cAAc;IAEnD,MAAM,EACJwB,WAAW,EACXC,+BAA+B,EAC/BC,6BAA6B,EAC9B,GAAGlC,MAAMW,OAAO,CAAC;QAChB,MAAMwB,gBAAgB1B,iBAAiBK,MAAM,CAAC,CAACC,IAAMA,EAAES,QAAQ,IAAIK;QACnE,MAAMO,yBAAyB1B,mBAAmBI,MAAM,CACtD,CAACC,IAAMA,EAAES,QAAQ,IAAIK;QAGvB,OAAO;YACLG,aACEtB,mBAAmB2B,MAAM,KAAKD,uBAAuBC,MAAM,IAC1DR,OAAOrB,cAAc;YAExB0B,+BACE9B,4BAA4BM;YAE9BuB,iCACE7B,4BAA4B+B;QAChC;IACF,GAAG;QAACN;QAAKnB;QAAoBD;QAAkBD;KAAW;IAE1D,qBACE,MAACR,MAAMsC,QAAQ;;YACZ9B,2BACC,MAACR,MAAMsC,QAAQ;;kCACb,KAACC;kCAAG;;kCACJ,KAAClC;wBACCmC,oBAAoBP;wBACpBQ,MAAMZ;;kCAER,KAAC5B;wBACCyC,YAAYlC,WAAWmB,kBAAkB;wBACzCgB,WAAWnC,WAAWkB,iBAAiB;;;iBAGzCkB;YAEHV,8BAA8BG,MAAM,iBACnC,MAACrC,MAAMsC,QAAQ;;kCACb,KAACC;kCAAG;;kCACJ,KAAClC;wBACCmC,oBAAoBN;wBACpBO,MAAMZ;;;iBAGRe;YACHZ,4BACC,KAAChC,MAAMsC,QAAQ;0BACb,cAAA,MAACO;oBACCC,UAAU;oBACVC,iDAA+C;oBAC/CC,MAAK;oBACLC,SAAS,IAAMnB,OAAO,CAACD;;wBAEtBA,MAAM,SAAS;wBAAO;;;iBAGzBe;;;AAGV;AAEA,OAAO,MAAMM,SAAS/C,uBAqHrB"}