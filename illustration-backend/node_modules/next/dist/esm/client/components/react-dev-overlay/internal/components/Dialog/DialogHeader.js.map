{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.tsx"], "names": ["React", "DialogHeader", "children", "className", "div", "data-nextjs-dialog-header"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAO9B,MAAMC,eAA4C,SAASA,aAAa,KAGvE;IAHuE,IAAA,EACtEC,QAAQ,EACRC,SAAS,EACV,GAHuE;IAItE,qBACE,KAACC;QAAIC,2BAAyB;QAACF,WAAWA;kBACvCD;;AAGP;AAEA,SAASD,YAAY,GAAE"}