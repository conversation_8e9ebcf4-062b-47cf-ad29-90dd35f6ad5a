{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/Errors.tsx"], "names": ["useState", "useEffect", "useMemo", "useCallback", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "LeftRightDialogHeader", "Overlay", "Toast", "getErrorByType", "noop", "css", "CloseIcon", "RuntimeError", "VersionStalenessInfo", "getErrorSource", "HotlinkedText", "PseudoHtmlDiff", "getHydrationWarningType", "isNextjsLink", "text", "startsWith", "getErrorSignature", "ev", "event", "type", "reason", "name", "message", "stack", "_", "Errors", "isAppDir", "errors", "initialDisplayState", "versionInfo", "activeError", "lookups", "setLookups", "readyErrors", "nextError", "ready", "next", "idx", "length", "e", "id", "push", "prev", "isLoading", "Boolean", "mounted", "then", "resolved", "m", "displayState", "setDisplayState", "activeIdx", "setActiveIndex", "previous", "v", "Math", "max", "min", "minimize", "hide", "fullscreen", "className", "onClick", "div", "svg", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "circle", "cx", "cy", "r", "line", "x1", "y1", "x2", "y2", "span", "button", "data-nextjs-toast-errors-hide-button", "stopPropagation", "aria-label", "error", "isServerError", "includes", "errorDetails", "details", "warningTemplate", "serverContent", "clientContent", "warning", "hydrationErrorType", "hydrationWarning", "replace", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "undefined", "close", "small", "data-nextjs-dialog-header-total-count", "h1", "p", "matcher", "componentStackFrames", "hydrationMismatchType", "firstContent", "second<PERSON><PERSON>nt", "toString", "styles"], "mappings": ";;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,QAAO;AACjE,SACEC,sBAAsB,EACtBC,0BAA0B,QAGrB,eAAc;AACrB,SACEC,MAAM,EACNC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,uBAAsB;AAC7B,SAASC,qBAAqB,QAAQ,sCAAqC;AAC3E,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,KAAK,QAAQ,sBAAqB;AAC3C,SAASC,cAAc,QAAQ,4BAA2B;AAE1D,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AACtD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,qCAAoC;AAEzE,SAASC,cAAc,QAAQ,yCAAwC;AACvE,SAASC,aAAa,QAAQ,gCAA+B;AAC7D,SAASC,cAAc,QAAQ,6CAA4C;AAC3E,SAEEC,uBAAuB,QAClB,kCAAiC;AAiBxC,SAASC,aAAaC,IAAY;IAChC,OAAOA,KAAKC,UAAU,CAAC;AACzB;AAEA,SAASC,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,OAAQC,MAAMC,IAAI;QAChB,KAAKzB;QACL,KAAKC;YAA4B;gBAC/B,OAAO,AAAGuB,MAAME,MAAM,CAACC,IAAI,GAAC,OAAIH,MAAME,MAAM,CAACE,OAAO,GAAC,OAAIJ,MAAME,MAAM,CAACG,KAAK;YAC7E;QACA;YAAS,CACT;IACF;IAEA,6DAA6D;IAC7D,MAAMC,IAAWN;IACjB,OAAO;AACT;AAEA,OAAO,SAASO,OAAO,KAKT;IALS,IAAA,EACrBC,QAAQ,EACRC,MAAM,EACNC,mBAAmB,EACnBC,WAAW,EACC,GALS;QA2NNC;IArNf,MAAM,CAACC,SAASC,WAAW,GAAG1C,SAC5B,CAAC;IAGH,MAAM,CAAC2C,aAAaC,UAAU,GAAG1C,QAE/B;QACA,IAAI2C,QAA2B,EAAE;QACjC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMV,OAAOW,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAIZ,MAAM,CAACU,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMT,SAAS;gBACjBI,MAAMM,IAAI,CAACV,OAAO,CAACS,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOf,MAAM,CAACU,MAAM,EAAE;gBAC5B,IAAIrB,kBAAkB0B,UAAU1B,kBAAkBuB,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACT;QAAQI;KAAQ;IAEpB,MAAMY,YAAYnD,QAAiB;QACjC,OAAOyC,YAAYK,MAAM,GAAG,KAAKM,QAAQjB,OAAOW,MAAM;IACxD,GAAG;QAACX,OAAOW,MAAM;QAAEL,YAAYK,MAAM;KAAC;IAEtC/C,UAAU;QACR,IAAI2C,aAAa,MAAM;YACrB;QACF;QACA,IAAIW,UAAU;QAEd1C,eAAe+B,WAAWR,UAAUoB,IAAI,CACtC,CAACC;YACC,sEAAsE;YACtE,uEAAuE;YACvE,kBAAkB;YAClB,IAAIF,SAAS;gBACXb,WAAW,CAACgB,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAASP,EAAE,CAAC,EAAEO;oBAAS,CAAA;YACrD;QACF,GACA;QACE,yCAAyC;QAC3C;QAGF,OAAO;YACLF,UAAU;QACZ;IACF,GAAG;QAACX;QAAWR;KAAS;IAExB,MAAM,CAACuB,cAAcC,gBAAgB,GACnC5D,SAAuBsC;IACzB,MAAM,CAACuB,WAAWC,eAAe,GAAG9D,SAAiB;IACrD,MAAM+D,WAAW5D,YACf,IAAM2D,eAAe,CAACE,IAAMC,KAAKC,GAAG,CAAC,GAAGF,IAAI,KAC5C,EAAE;IAEJ,MAAMlB,OAAO3C,YACX,IACE2D,eAAe,CAACE,IACdC,KAAKC,GAAG,CAAC,GAAGD,KAAKE,GAAG,CAACxB,YAAYK,MAAM,GAAG,GAAGgB,IAAI,MAErD;QAACrB,YAAYK,MAAM;KAAC;IAGtB,MAAMR,cAActC,QAClB;YAAMyC;eAAAA,CAAAA,yBAAAA,WAAW,CAACkB,UAAU,YAAtBlB,yBAA0B;IAAG,GACnC;QAACkB;QAAWlB;KAAY;IAG1B,kEAAkE;IAClE,gDAAgD;IAChD1C,UAAU;QACR,IAAIoC,OAAOW,MAAM,GAAG,GAAG;YACrBN,WAAW,CAAC;YACZkB,gBAAgB;YAChBE,eAAe;QACjB;IACF,GAAG;QAACzB,OAAOW,MAAM;KAAC;IAElB,MAAMoB,WAAWjE,YAAY,IAAMyD,gBAAgB,cAAc,EAAE;IACnE,MAAMS,OAAOlE,YAAY,IAAMyD,gBAAgB,WAAW,EAAE;IAC5D,MAAMU,aAAanE,YAAY,IAAMyD,gBAAgB,eAAe,EAAE;IAEtE,2EAA2E;IAC3E,6CAA6C;IAC7C,IAAIvB,OAAOW,MAAM,GAAG,KAAKR,eAAe,MAAM;QAC5C,OAAO;IACT;IAEA,IAAIa,WAAW;QACb,6BAA6B;QAC7B,qBAAO,KAAC1C;IACV;IAEA,IAAIgD,iBAAiB,UAAU;QAC7B,OAAO;IACT;IAEA,IAAIA,iBAAiB,aAAa;QAChC,qBACE,KAAC/C;YAAM2D,WAAU;YAA6BC,SAASF;sBACrD,cAAA,MAACG;gBAAIF,WAAU;;kCACb,MAACG;wBACCC,OAAM;wBACNC,OAAM;wBACNC,QAAO;wBACPC,SAAQ;wBACRC,MAAK;wBACLC,QAAO;wBACPC,aAAY;wBACZC,eAAc;wBACdC,gBAAe;;0CAEf,KAACC;gCAAOC,IAAG;gCAAKC,IAAG;gCAAKC,GAAE;;0CAC1B,KAACC;gCAAKC,IAAG;gCAAKC,IAAG;gCAAIC,IAAG;gCAAKC,IAAG;;0CAChC,KAACJ;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;gCAAQC,IAAG;;;;kCAEtC,MAACC;;4BACElD,YAAYK,MAAM;4BAAC;4BAAOL,YAAYK,MAAM,GAAG,IAAI,MAAM;;;kCAE5D,KAAC8C;wBACCC,sCAAoC;wBACpCxB,WAAU;wBACV1C,MAAK;wBACL2C,SAAS,CAACvB;4BACRA,EAAE+C,eAAe;4BACjB3B;wBACF;wBACA4B,cAAW;kCAEX,cAAA,KAACjF;;;;;IAKX;IAEA,MAAMkF,QAAQ1D,YAAY0D,KAAK;IAC/B,MAAMC,gBAAgB;QAAC;QAAU;KAAc,CAACC,QAAQ,CACtDjF,eAAe+E,UAAU;IAG3B,MAAMG,eAAoC,AAACH,MAAcI,OAAO,IAAI,CAAC;IACrE,MAAM,CAACC,iBAAiBC,eAAeC,cAAc,GACnDJ,aAAaK,OAAO,IAAI;QAAC;QAAM;QAAI;KAAG;IAExC,MAAMC,qBAAqBrF,wBAAwBiF;IACnD,MAAMK,mBAAmBL,kBACrBA,gBACGM,OAAO,CAAC,MAAML,eACdK,OAAO,CAAC,MAAMJ,eACdI,OAAO,CAAC,MAAM,IAAI,0BAA0B;KAC5CA,OAAO,CAAC,OAAO,IAAI,8CAA8C;KACjEA,OAAO,CAAC,cAAc,MACzB;IAEJ,qBACE,KAAClG;kBACC,cAAA,KAACL;YACCuB,MAAK;YACLiF,mBAAgB;YAChBC,oBAAiB;YACjBC,SAASb,gBAAgBc,YAAY7C;sBAErC,cAAA,MAAC5D;;kCACC,MAACC;wBAAa8D,WAAU;;0CACtB,MAAC7D;gCACCqD,UAAUF,YAAY,IAAIE,WAAW;gCACrCjB,MAAMe,YAAYlB,YAAYK,MAAM,GAAG,IAAIF,OAAO;gCAClDoE,OAAOf,gBAAgBc,YAAY7C;;kDAEnC,MAAC+C;;0DACC,KAACtB;0DAAMhC,YAAY;;4CAAS;4CAAI;0DAChC,KAACgC;gDAAKuB,uCAAqC;0DACxCzE,YAAYK,MAAM;;4CAEpB;4CACAL,YAAYK,MAAM,GAAG,IAAI,KAAK;;;oCAEhCT,4BAAc,KAACrB;wCAAsB,GAAGqB,WAAW;yCAAO;;;0CAE7D,KAAC8E;gCAAGnE,IAAG;0CACJiD,gBAAgB,iBAAiB;;0CAEpC,MAACmB;gCACCpE,IAAG;gCACHqB,WAAU;;oCAET2B,MAAMnE,IAAI;oCAAC;oCAAE;kDACd,KAACX;wCAAcI,MAAM0E,MAAMlE,OAAO;wCAAEuF,SAAShG;;;;4BAE9CqF,kCACC;;kDACE,KAACU;wCACCpE,IAAG;wCACHqB,WAAU;kDAETqC;;oCAEFpE,EAAAA,oCAAAA,YAAYgF,oBAAoB,qBAAhChF,kCAAkCQ,MAAM,kBACvC,KAAC3B;wCACCkD,WAAU;wCACVkD,uBAAuBd;wCACvBa,sBAAsBhF,YAAYgF,oBAAoB;wCACtDE,cAAclB;wCACdmB,eAAelB;yCAEf;;;4BAGPN,8BACC,KAAC1B;0CACC,cAAA,KAAC0C;8CAAM;;iCAKPF;;;kCAEN,KAAC1G;wBAAWgE,WAAU;kCACpB,cAAA,KAACtD;4BAA6CiF,OAAO1D;2BAAlCA,YAAYU,EAAE,CAAC0E,QAAQ;;;;;;AAMtD;AAEA,OAAO,MAAMC,SAAS9G,uBAoFrB"}