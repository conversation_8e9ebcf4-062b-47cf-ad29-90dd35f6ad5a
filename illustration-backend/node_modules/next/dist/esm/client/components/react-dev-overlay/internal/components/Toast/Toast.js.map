{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Toast/Toast.tsx"], "names": ["React", "Toast", "onClick", "children", "className", "div", "data-nextjs-toast", "e", "preventDefault", "data-nextjs-toast-wrapper"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAQ9B,OAAO,MAAMC,QAA8B,SAASA,MAAM,KAIzD;IAJyD,IAAA,EACxDC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACV,GAJyD;IAKxD,qBACE,KAACC;QACCC,mBAAiB;QACjBJ,SAAS,CAACK;YACRA,EAAEC,cAAc;YAChB,OAAON,2BAAAA;QACT;QACAE,WAAWA;kBAEX,cAAA,KAACC;YAAII,2BAAyB;sBAAEN;;;AAGtC,EAAC"}