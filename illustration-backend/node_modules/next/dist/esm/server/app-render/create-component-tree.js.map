{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["React", "isClientReference", "getLayoutOrPageModule", "interopDefault", "parseLoaderTree", "createComponentStylesAndScripts", "getLayerAssets", "hasLoadingComponentInTree", "validateRevalidate", "PARALLEL_ROUTE_DEFAULT_PATH", "getTracer", "NextNodeServerSpan", "StaticGenBailoutError", "createComponentTree", "props", "trace", "spanName", "createComponentTreeInternal", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "missingSlots", "renderOpts", "nextConfigOutput", "experimental", "staticGenerationStore", "componentMod", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "ClientPageRoot", "createUntrackedSearchParams", "createDynamicallyTrackedSearchParams", "serverHooks", "DynamicServerError", "Postpone", "pagePath", "getDynamicParamFromSegment", "isPrefetch", "query", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "Template", "templateStyles", "templateScripts", "filePath", "getComponent", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "layoutOrPageMod", "hideSpan", "attributes", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "dynamicShouldError", "forceDynamic", "isStaticGeneration", "prerenderState", "err", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceStatic", "fetchCache", "revalidate", "urlPathname", "defaultRevalidate", "dynamicUsageErr", "LayoutOrPage", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "params", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "notFoundComponent", "childCacheNodeSeedData", "ppr", "parsedTree", "endsWith", "add", "seedData", "child", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "loadingData", "children", "reason", "pathname", "isClientComponent", "meta", "name", "content", "console", "segmentElement", "searchParams"], "mappings": ";AACA,OAAOA,WAAW,QAAO;AACzB,SAASC,iBAAiB,QAAQ,6BAA4B;AAC9D,SAASC,qBAAqB,QAAQ,wBAAuB;AAE7D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,qBAAoB;AACvD,SAASC,2BAA2B,QAAQ,iDAAgD;AAC5F,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,qBAAqB,QAAQ,oDAAmD;AAOzF;;CAEC,GACD,OAAO,SAASC,oBAAoBC,KAanC;IACC,OAAOJ,YAAYK,KAAK,CACtBJ,mBAAmBE,mBAAmB,EACtC;QACEG,UAAU;IACZ,GACA,IAAMC,4BAA4BH;AAEtC;AAEA,eAAeG,4BAA4B,EACzCC,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EACHC,YAAY,EAcb;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,qBAAqB,EACrBC,cAAc,EACZC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,cAAc,EACdC,2BAA2B,EAC3BC,oCAAoC,EACpCC,aAAa,EAAEC,kBAAkB,EAAE,EACnCC,QAAQ,EACT,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,UAAU,EACVC,KAAK,EACN,GAAGnB;IAEJ,MAAM,EAAEoB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEjD,gBAAgBgB;IAElB,MAAM,EAAEkC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGN;IAEpE,MAAMO,+BAA+B,IAAIC,IAAIpC;IAC7C,MAAMqC,8BAA8B,IAAID,IAAInC;IAC5C,MAAMqC,2CAA2C,IAAIF,IACnDlC;IAGF,MAAMqC,cAAczD,eAAe;QACjCuB;QACAqB;QACA1B,aAAamC;QACblC,YAAYoC;QACZnC,yBAAyBoC;IAC3B;IAEA,MAAM,CAACE,UAAUC,gBAAgBC,gBAAgB,GAAGX,WAChD,MAAMlD,gCAAgC;QACpCwB;QACAsC,UAAUZ,QAAQ,CAAC,EAAE;QACrBa,cAAcb,QAAQ,CAAC,EAAE;QACzB/B,aAAamC;QACblC,YAAYoC;IACd,KACA;QAAC7D,MAAMqE,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGhB,QAChD,MAAMnD,gCAAgC;QACpCwB;QACAsC,UAAUX,KAAK,CAAC,EAAE;QAClBY,cAAcZ,KAAK,CAAC,EAAE;QACtBhC,aAAamC;QACblC,YAAYoC;IACd,KACA,EAAE;IAEN,MAAM,CAACY,SAASC,eAAeC,eAAe,GAAGlB,UAC7C,MAAMpD,gCAAgC;QACpCwB;QACAsC,UAAUV,OAAO,CAAC,EAAE;QACpBW,cAAcX,OAAO,CAAC,EAAE;QACxBjC,aAAamC;QACblC,YAAYoC;IACd,KACA,EAAE;IAEN,MAAMe,WAAW,OAAOtB,WAAW;IACnC,MAAMuB,SAAS,OAAO5B,SAAS;IAC/B,MAAM,CAAC6B,gBAAgB,GAAG,MAAMpE,YAAYK,KAAK,CAC/CJ,mBAAmBT,qBAAqB,EACxC;QACE6E,UAAU,CAAEH,CAAAA,YAAYC,MAAK;QAC7B7D,UAAU;QACVgE,YAAY;YACV,gBAAgB7B;QAClB;IACF,GACA,IAAMjD,sBAAsBkB;IAG9B;;GAEC,GACD,MAAM6D,wBAAwBL,YAAY,CAACrD;IAC3C;;GAEC,GACD,MAAM2D,uCACJ3D,sBAAsB0D;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAG1B,WAC/B,MAAMrD,gCAAgC;QACpCwB;QACAsC,UAAUT,QAAQ,CAAC,EAAE;QACrBU,cAAcV,QAAQ,CAAC,EAAE;QACzBlC,aAAamC;QACblC,YAAYoC;IACd,KACA,EAAE;IAEN,IAAIwB,UAAUP,mCAAAA,gBAAiBO,OAAO;IAEtC,IAAIrD,qBAAqB,UAAU;QACjC,IAAI,CAACqD,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC,kFAAkF;YAClF,MAAM,IAAIzE,sBACR,CAAC,6SAA6S,CAAC;QAEnT;IACF;IAEA,IAAI,OAAOyE,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBnD,sBAAsBoD,kBAAkB,GAAG;QAC7C,OAAO,IAAID,YAAY,iBAAiB;YACtCnD,sBAAsBqD,YAAY,GAAG;YAErC,0DAA0D;YAC1D,IACErD,sBAAsBsD,kBAAkB,IACxC,CAACtD,sBAAsBuD,cAAc,EACrC;gBACA,wEAAwE;gBACxE,0CAA0C;gBAC1C,MAAMC,MAAM,IAAI/C,mBACd,CAAC,qEAAqE,CAAC;gBAEzET,sBAAsByD,uBAAuB,GAAGD,IAAIE,OAAO;gBAC3D1D,sBAAsB2D,iBAAiB,GAAGH,IAAII,KAAK;gBACnD,MAAMJ;YACR;QACF,OAAO;YACLxD,sBAAsBoD,kBAAkB,GAAG;YAC3CpD,sBAAsB6D,WAAW,GAAGV,YAAY;QAClD;IACF;IAEA,IAAI,QAAOP,mCAAAA,gBAAiBkB,UAAU,MAAK,UAAU;QACnD9D,sBAAsB8D,UAAU,GAAGlB,mCAAAA,gBAAiBkB,UAAU;IAChE;IAEA,IAAI,QAAOlB,mCAAAA,gBAAiBmB,UAAU,MAAK,aAAa;QACtDzF,mBACEsE,mCAAAA,gBAAiBmB,UAAU,EAC3B/D,sBAAsBgE,WAAW;IAErC;IAEA,IAAI,QAAOpB,mCAAAA,gBAAiBmB,UAAU,MAAK,UAAU;QACnDpE,IAAIsE,iBAAiB,GAAGrB,gBAAgBmB,UAAU;QAElD,IACE,OAAO/D,sBAAsB+D,UAAU,KAAK,eAC3C,OAAO/D,sBAAsB+D,UAAU,KAAK,YAC3C/D,sBAAsB+D,UAAU,GAAGpE,IAAIsE,iBAAiB,EAC1D;YACAjE,sBAAsB+D,UAAU,GAAGpE,IAAIsE,iBAAiB;QAC1D;QAEA,IACE,CAACjE,sBAAsB6D,WAAW,IAClC7D,sBAAsBsD,kBAAkB,IACxC3D,IAAIsE,iBAAiB,KAAK,KAC1B,wEAAwE;QACxE,0CAA0C;QAC1C,CAACjE,sBAAsBuD,cAAc,EACrC;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAExC,QAAQ,CAAC;YACrEjB,sBAAsByD,uBAAuB,GAAGA;YAEhD,MAAM,IAAIhD,mBAAmBgD;QAC/B;IACF;IAEA,oEAAoE;IACpE,IAAIzD,sBAAsBkE,eAAe,EAAE;QACzC,MAAMlE,sBAAsBkE,eAAe;IAC7C;IAEA,MAAMC,eAAqDvB,kBACvD,MAAM3E,eAAe2E,mBACrBwB;IAEJ;;GAEC,GACD,IAAIC,YAAYF;IAChB,MAAMG,eAAeC,OAAOC,IAAI,CAACrD;IACjC,MAAMsD,aAAaH,aAAaI,MAAM,GAAG;IAEzC,gGAAgG;IAChG,6EAA6E;IAC7E,4GAA4G;IAC5G,gHAAgH;IAChH,mCAAmC;IACnC,IAAID,cAAc1B,yBAAyBoB,cAAc;QACvDE,YAAY,CAACM;YACX,MAAMC,oBAAoB3B;YAC1B,MAAM4B,sBAAsBV;YAC5B,qBACE,KAACjE;gBACCsB,UACEoD,kCACE;;wBACG/C;sCAKD,MAACgD;4BAAoBC,QAAQH,eAAeG,MAAM;;gCAC/C5B;8CACD,KAAC0B;;;;qBAGHR;0BAGN,cAAA,KAACS;oBAAqB,GAAGF,cAAc;;;QAG7C;IACF;IAEA,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAACxC,CAAAA,UAAU,OAAO0B,cAAc,WAAU,KAC1C,CAACa,mBAAmBb,YACpB;YACA,MAAM,IAAIe,MACR,CAAC,sDAAsD,EAAEzE,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAOyB,mBAAmB,eAC1B,CAAC8C,mBAAmB9C,iBACpB;YACA,MAAM,IAAIgD,MACR,CAAC,8DAA8D,EAAEnE,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAOsB,YAAY,eAAe,CAAC2C,mBAAmB3C,UAAU;YAClE,MAAM,IAAI6C,MACR,CAAC,0DAA0D,EAAEnE,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAOgC,aAAa,eAAe,CAACiC,mBAAmBjC,WAAW;YACpE,MAAM,IAAImC,MACR,CAAC,2DAA2D,EAAEnE,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAMoE,eAAezE,2BAA2BK;IAChD;;GAEC,GACD,MAAMqE,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGpG,YAAY;QACf,CAACkG,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEApG;IACN,4BAA4B;IAC5B,MAAMsG,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGzE;IAEhE,EAAE;IACF,8EAA8E;IAC9E,kBAAkB;IAClB,MAAM0E,mBAAmB,MAAMC,QAAQC,GAAG,CACxCtB,OAAOC,IAAI,CAACrD,gBAAgB2E,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwC7G,YAC1C;YAAC2G;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgB/E,cAAc,CAAC4E,iBAAiB;QAEtD,MAAMI,oBACJlD,YAAY+C,mCAAqB,KAAC/C,gBAAcmB;QAElD,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIgC,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,qEAAqE;QACrE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3BvF,cACC0B,CAAAA,WAAW,CAAClE,0BAA0B6H,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAACnG,aAAasG,GAAG,EACjB;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAItB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBrF,cAAc;oBAKxD0G;gBAJF,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMA,aAAapI,gBAAgBgI;gBACnC,KACEI,+BAAAA,WAAWtF,gBAAgB,qBAA3BsF,6BAA6BC,QAAQ,CAAChI,8BACtC;oBACAqB,aAAa4G,GAAG,CAACT;gBACnB;YACF;YAEA,MAAMU,WAAW,MAAM1H,4BAA4B;gBACjDC,mBAAmB,CAAC0H;oBAClB,OAAO1H,kBAAkB;2BAAIiH;2BAAuBS;qBAAM;gBAC5D;gBACAzH,YAAYiH;gBACZ/G,cAAcmG;gBACdjG,oBAAoB2D;gBACpB1D,aAAamC;gBACblC,YAAYoC;gBACZnC,yBAAyBoC;gBACzBnC;gBACAC,gBAAgBsG,qBAAqBtG,iBAAiB0E;gBACtDzE;gBACAC;YACF;YAEAwG,yBAAyBK;QAC3B;QAEA,4CAA4C;QAC5C,OAAO;YACLV;0BACA,KAAC5F;gBACCwG,mBAAmBZ;gBACnBa,aAAa5H,kBAAkBiH;gBAC/B,sKAAsK;gBACtK3E,OAAOc;gBACPC,aAAaA;gBACbC,cAAcA;gBACdjB,wBACE,KAACS;8BACC,cAAA,KAAC1B;;gBAGL2B,gBAAgBA;gBAChBC,iBAAiBA;gBACjBR,UAAU2E;gBACVjD,gBAAgBA;;YAElBkD;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIS,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMZ,iBAAiBP,iBAAkB;QAC5C,MAAM,CAACI,kBAAkBgB,mBAAmBC,WAAW,GAAGd;QAC1DW,kBAAkB,CAACd,iBAAiB,GAAGgB;QACvCD,8BAA8B,CAACf,iBAAiB,GAAGiB;IACrD;IAEA,MAAMC,cAAiC1E,UACnC;sBAAC,KAACA;QAAYC;QAAeC;KAAe,GAC5C;IAEJ,wIAAwI;IACxI,IAAI,CAAC4B,WAAW;QACd,OAAO;YACLoB;YACAqB;YACA,wEAAwE;YACxE,sEAAsE;YACtE,wEAAwE;YACxE,uEAAuE;YACvE,oBAAoB;0BACpB;;oBACGjF;oBACAgF,mBAAmBK,QAAQ;;;YAE9BD;SACD;IACH;IAEA,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,6FAA6F;IAC7F,mGAAmG;IACnG,gGAAgG;IAChG,2GAA2G;IAC3G,yGAAyG;IACzG,uGAAuG;IACvG,qBAAqB;IACrB,IACEjH,sBAAsBqD,YAAY,IAClCrD,sBAAsBuD,cAAc,EACpC;QACA,OAAO;YACLkC;YACAqB;0BACA;;kCACE,KAACpG;wBACC6C,gBAAgBvD,sBAAsBuD,cAAc;wBACpD4D,QAAO;wBACPC,UAAUpH,sBAAsBgE,WAAW;;oBAE5CnC;;;YAEHoF;SACD;IACH;IAEA,MAAMI,oBAAoBtJ,kBAAkB6E;IAE5C,0EAA0E;IAC1E,MAAMhE,QAAiCiI;IAEvC,oEAAoE;IACpE,iEAAiE;IACjE,IACE5D,YACAxD,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAACkG,iBAAiBjB,MAAM,EACxB;QACA9F,MAAMsI,QAAQ,iBACZ;;8BACE,KAACI;oBAAKC,MAAK;oBAASC,SAAQ;;gBAC3BzC,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,KAACqC;oBAAKC,MAAK;oBAAaC,SAAQ;;gBAEjCtE;8BACD,KAACD;;;IAGP;IAEA,yBAAyB;IACzB,IACE8B,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,YAAY4B,oBACZ;QACA,6EAA6E;QAC7EY,QAAQnG,KAAK,CACX,CAAC,uGAAuG,EAAEL,QAAQ,CAAC;IAEvH;IACArC,MAAMkG,MAAM,GAAGQ;IAEf,IAAIoC;IACJ,IAAI/E,QAAQ;QACV,iDAAiD;QACjD,IAAI0E,mBAAmB;YACrB,wGAAwG;YACxG,oFAAoF;YACpF,6FAA6F;YAC7F,oFAAoF;YACpF,kGAAkG;YAClGzI,MAAM+I,YAAY,GAAGrH,4BAA4BQ;YACjD4G,+BACE;;oBACGhI;kCACD,KAACW;wBAAezB,OAAOA;wBAAOyF,WAAWA;;oBACxCxC;;;QAGP,OAAO;YACL,iGAAiG;YACjG,oDAAoD;YACpDjD,MAAM+I,YAAY,GAAGpH,qCAAqCO;YAC1D4G,+BACE;;oBACGhI;kCACD,KAAC2E;wBAAW,GAAGzF,KAAK;;oBACnBiD;;;QAGP;IACF,OAAO;QACL,2CAA2C;QAC3C6F,+BACE;;gBACG7F;8BACD,KAACwC;oBAAW,GAAGzF,KAAK;;;;IAG1B;IAEA,OAAO;QACL6G;QACAqB;sBACA;;gBACGY;gBASA;;;QAEHT;KACD;AACH"}