{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "names": ["stringHash", "formatServerError", "SpanStatusCode", "getTracer", "isAbortError", "isDynamicUsageError", "ErrorHandlerSource", "serverComponents", "flightData", "html", "createErrorHandler", "source", "dev", "isNextExport", "errorLogger", "digestErrorsMap", "allCapturedErrors", "silenceLogger", "err", "errorInfo", "digest", "message", "stack", "toString", "push", "has", "set", "get", "includes", "span", "getActiveScopeSpan", "recordException", "setStatus", "code", "ERROR", "catch", "__next_log_error__", "console", "error"], "mappings": "AAAA,OAAOA,gBAAgB,iCAAgC;AACvD,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,SAASC,cAAc,EAAEC,SAAS,QAAQ,sBAAqB;AAC/D,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,8CAA6C;AAWjF,OAAO,MAAMC,qBAAqB;IAChCC,kBAAkB;IAClBC,YAAY;IACZC,MAAM;AACR,EAAU;AAEV;;;;CAIC,GACD,OAAO,SAASC,mBAAmB,EACjC;;GAEC,GACDC,MAAM,EACNC,GAAG,EACHC,YAAY,EACZC,WAAW,EACXC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EASd;IACC,OAAO,CAACC,KAAUC;YAsCZD;QArCJ,kEAAkE;QAClE,uDAAuD;QACvD,IAAI,CAACA,IAAIE,MAAM,EAAE;YACf,+EAA+E;YAC/EF,IAAIE,MAAM,GAAGpB,WACXkB,IAAIG,OAAO,GAAIF,CAAAA,CAAAA,6BAAAA,UAAWG,KAAK,KAAIJ,IAAII,KAAK,IAAI,EAAC,GACjDC,QAAQ;QACZ;QACA,MAAMH,SAASF,IAAIE,MAAM;QAEzB,IAAIJ,mBAAmBA,kBAAkBQ,IAAI,CAACN;QAE9C,kDAAkD;QAClD,wCAAwC;QACxC,IAAIb,oBAAoBa,MAAM,OAAOA,IAAIE,MAAM;QAE/C,8DAA8D;QAC9D,IAAIhB,aAAac,MAAM;QAEvB,IAAI,CAACH,gBAAgBU,GAAG,CAACL,SAAS;YAChCL,gBAAgBW,GAAG,CAACN,QAAQF;QAC9B,OAAO,IAAIP,WAAWL,mBAAmBG,IAAI,EAAE;YAC7C,gEAAgE;YAChE,yEAAyE;YACzES,MAAMH,gBAAgBY,GAAG,CAACP;QAC5B;QAEA,yEAAyE;QACzE,IAAIR,KAAK;YACPX,kBAAkBiB;QACpB;QACA,kCAAkC;QAClC,6BAA6B;QAC7B,+CAA+C;QAC/C,IACE,CACEL,CAAAA,iBACAK,wBAAAA,eAAAA,IAAKG,OAAO,qBAAZH,aAAcU,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMC,OAAO1B,YAAY2B,kBAAkB;YAC3C,IAAID,MAAM;gBACRA,KAAKE,eAAe,CAACb;gBACrBW,KAAKG,SAAS,CAAC;oBACbC,MAAM/B,eAAegC,KAAK;oBAC1Bb,SAASH,IAAIG,OAAO;gBACtB;YACF;YAEA,IAAI,CAACJ,eAAe;gBAClB,IAAIH,aAAa;oBACfA,YAAYI,KAAKiB,KAAK,CAAC,KAAO;gBAChC,OAAO;oBACL,kEAAkE;oBAClE,gDAAgD;oBAChD,4DAA4D;oBAC5D,IAAI,OAAOC,uBAAuB,YAAY;wBAC5CA,mBAAmBlB;oBACrB,OAAO;wBACLmB,QAAQC,KAAK,CAACpB;oBAChB;gBACF;YACF;QACF;QAEA,OAAOA,IAAIE,MAAM;IACnB;AACF"}