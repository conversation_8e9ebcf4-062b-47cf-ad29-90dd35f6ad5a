{"version": 3, "sources": ["../../src/lib/picocolors.ts"], "names": ["globalThis", "env", "stdout", "process", "enabled", "NO_COLOR", "FORCE_COLOR", "isTTY", "CI", "TERM", "replaceClose", "str", "close", "replace", "index", "start", "substring", "end", "length", "nextIndex", "indexOf", "formatter", "open", "String", "input", "string", "reset", "s", "bold", "dim", "italic", "underline", "inverse", "hidden", "strikethrough", "black", "red", "green", "yellow", "blue", "magenta", "purple", "cyan", "white", "gray", "bgBlack", "bgRed", "bgGreen", "bgYellow", "bgBlue", "bgMagenta", "bg<PERSON>yan", "bgWhite"], "mappings": "AAAA,cAAc;AAEd,wEAAwE;AAExE,2EAA2E;AAC3E,yEAAyE;AACzE,oEAAoE;AAEpE,2EAA2E;AAC3E,mEAAmE;AACnE,0EAA0E;AAC1E,yEAAyE;AACzE,wEAAwE;AACxE,0EAA0E;AAC1E,iEAAiE;AACjE,EAAE;AACF,8GAA8G;IAEtFA;AAAxB,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE,GAAGF,EAAAA,cAAAA,+BAAAA,YAAYG,OAAO,KAAI,CAAC;AAEhD,MAAMC,UACJH,OACA,CAACA,IAAII,QAAQ,IACZJ,CAAAA,IAAIK,WAAW,IAAKJ,CAAAA,0BAAAA,OAAQK,KAAK,KAAI,CAACN,IAAIO,EAAE,IAAIP,IAAIQ,IAAI,KAAK,MAAM;AAEtE,MAAMC,eAAe,CACnBC,KACAC,OACAC,SACAC;IAEA,MAAMC,QAAQJ,IAAIK,SAAS,CAAC,GAAGF,SAASD;IACxC,MAAMI,MAAMN,IAAIK,SAAS,CAACF,QAAQF,MAAMM,MAAM;IAC9C,MAAMC,YAAYF,IAAIG,OAAO,CAACR;IAC9B,OAAO,CAACO,YACJJ,QAAQL,aAAaO,KAAKL,OAAOC,SAASM,aAC1CJ,QAAQE;AACd;AAEA,MAAMI,YAAY,CAACC,MAAcV,OAAeC,UAAUS,IAAI;IAC5D,IAAI,CAAClB,SAAS,OAAOmB;IACrB,OAAO,CAACC;QACN,MAAMC,SAAS,KAAKD;QACpB,MAAMV,QAAQW,OAAOL,OAAO,CAACR,OAAOU,KAAKJ,MAAM;QAC/C,OAAO,CAACJ,QACJQ,OAAOZ,aAAae,QAAQb,OAAOC,SAASC,SAASF,QACrDU,OAAOG,SAASb;IACtB;AACF;AAEA,OAAO,MAAMc,QAAQtB,UAAU,CAACuB,IAAc,CAAC,OAAO,EAAEA,EAAE,OAAO,CAAC,GAAGJ,OAAM;AAC3E,OAAO,MAAMK,OAAOP,UAAU,WAAW,YAAY,mBAAkB;AACvE,OAAO,MAAMQ,MAAMR,UAAU,WAAW,YAAY,mBAAkB;AACtE,OAAO,MAAMS,SAAST,UAAU,WAAW,YAAW;AACtD,OAAO,MAAMU,YAAYV,UAAU,WAAW,YAAW;AACzD,OAAO,MAAMW,UAAUX,UAAU,WAAW,YAAW;AACvD,OAAO,MAAMY,SAASZ,UAAU,WAAW,YAAW;AACtD,OAAO,MAAMa,gBAAgBb,UAAU,WAAW,YAAW;AAC7D,OAAO,MAAMc,QAAQd,UAAU,YAAY,YAAW;AACtD,OAAO,MAAMe,MAAMf,UAAU,YAAY,YAAW;AACpD,OAAO,MAAMgB,QAAQhB,UAAU,YAAY,YAAW;AACtD,OAAO,MAAMiB,SAASjB,UAAU,YAAY,YAAW;AACvD,OAAO,MAAMkB,OAAOlB,UAAU,YAAY,YAAW;AACrD,OAAO,MAAMmB,UAAUnB,UAAU,YAAY,YAAW;AACxD,OAAO,MAAMoB,SAASpB,UAAU,0BAA0B,YAAW;AACrE,OAAO,MAAMqB,OAAOrB,UAAU,YAAY,YAAW;AACrD,OAAO,MAAMsB,QAAQtB,UAAU,YAAY,YAAW;AACtD,OAAO,MAAMuB,OAAOvB,UAAU,YAAY,YAAW;AACrD,OAAO,MAAMwB,UAAUxB,UAAU,YAAY,YAAW;AACxD,OAAO,MAAMyB,QAAQzB,UAAU,YAAY,YAAW;AACtD,OAAO,MAAM0B,UAAU1B,UAAU,YAAY,YAAW;AACxD,OAAO,MAAM2B,WAAW3B,UAAU,YAAY,YAAW;AACzD,OAAO,MAAM4B,SAAS5B,UAAU,YAAY,YAAW;AACvD,OAAO,MAAM6B,YAAY7B,UAAU,YAAY,YAAW;AAC1D,OAAO,MAAM8B,SAAS9B,UAAU,YAAY,YAAW;AACvD,OAAO,MAAM+B,UAAU/B,UAAU,YAAY,YAAW"}