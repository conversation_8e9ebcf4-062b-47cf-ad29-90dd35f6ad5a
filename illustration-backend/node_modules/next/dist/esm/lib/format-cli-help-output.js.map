{"version": 3, "sources": ["../../src/lib/format-cli-help-output.ts"], "names": ["bold", "formatCliHelpOutput", "cmd", "helper", "termWidth", "<PERSON><PERSON><PERSON><PERSON>", "helpWidth", "itemIndentWidth", "itemSeparatorWidth", "formatItem", "term", "description", "value", "fullText", "padEnd", "wrap", "formatList", "textArray", "join", "replace", "repeat", "output", "commandUsage", "commandDescription", "length", "concat", "argumentList", "visibleArguments", "map", "argument", "argumentTerm", "argumentDescription", "optionList", "visibleOptions", "option", "optionTerm", "optionDescription", "commandList", "visibleCommands", "subCmd", "subcommandTerm", "subcommandDescription"], "mappings": "AACA,SAASA,IAAI,QAAQ,oBAAmB;AAExC,2DAA2D;AAC3D,mEAAmE;AACnE,mEAAmE;AACnE,MAAMC,sBAAsB,CAACC,KAAcC;IACzC,MAAMC,YAAYD,OAAOE,QAAQ,CAACH,KAAKC;IACvC,MAAMG,YAAYH,OAAOG,SAAS,IAAI;IACtC,MAAMC,kBAAkB;IACxB,MAAMC,qBAAqB,EAAE,+BAA+B;;IAE5D,SAASC,WAAWC,IAAY,EAAEC,WAAmB;QACnD,IAAIC,QAAQF;QAEZ,IAAIC,aAAa;YACf,IAAID,SAAS,aAAa;gBACxBE,QAAQ,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;YACrB;YAEA,MAAMG,WAAW,CAAC,EAAED,MAAME,MAAM,CAC9BV,YAAYI,oBACZ,EAAEG,YAAY,CAAC;YAEjB,OAAOR,OAAOY,IAAI,CAChBF,UACAP,YAAYC,iBACZH,YAAYI;QAEhB;QAEA,OAAOE;IACT;IAEA,SAASM,WAAWC,SAAmB;QACrC,OAAOA,UAAUC,IAAI,CAAC,MAAMC,OAAO,CAAC,OAAO,IAAIC,MAAM,CAACb;IACxD;IAEA,QAAQ;IACR,IAAIc,SAAS;QAAC,CAAC,EAAErB,KAAK,UAAU,CAAC,EAAEG,OAAOmB,YAAY,CAACpB,KAAK,CAAC;QAAE;KAAG;IAElE,cAAc;IACd,MAAMqB,qBAAqBpB,OAAOoB,kBAAkB,CAACrB;IAErD,IAAIqB,mBAAmBC,MAAM,GAAG,GAAG;QACjCH,SAASA,OAAOI,MAAM,CAAC;YAACtB,OAAOY,IAAI,CAACQ,oBAAoBjB,WAAW;YAAI;SAAG;IAC5E;IAEA,YAAY;IACZ,MAAMoB,eAAevB,OAAOwB,gBAAgB,CAACzB,KAAK0B,GAAG,CAAC,CAACC;QACrD,OAAOpB,WACLN,OAAO2B,YAAY,CAACD,WACpB1B,OAAO4B,mBAAmB,CAACF;IAE/B;IAEA,IAAIH,aAAaF,MAAM,GAAG,GAAG;QAC3BH,SAASA,OAAOI,MAAM,CAAC;YACrB,CAAC,EAAEzB,KAAK,cAAc,CAAC;YACvBgB,WAAWU;YACX;SACD;IACH;IAEA,UAAU;IACV,MAAMM,aAAa7B,OAAO8B,cAAc,CAAC/B,KAAK0B,GAAG,CAAC,CAACM;QACjD,OAAOzB,WACLN,OAAOgC,UAAU,CAACD,SAClB/B,OAAOiC,iBAAiB,CAACF;IAE7B;IAEA,IAAIF,WAAWR,MAAM,GAAG,GAAG;QACzBH,SAASA,OAAOI,MAAM,CAAC;YAAC,CAAC,EAAEzB,KAAK,YAAY,CAAC;YAAEgB,WAAWgB;YAAa;SAAG;IAC5E;IAEA,WAAW;IACX,MAAMK,cAAclC,OAAOmC,eAAe,CAACpC,KAAK0B,GAAG,CAAC,CAACW;QACnD,OAAO9B,WACLN,OAAOqC,cAAc,CAACD,SACtBpC,OAAOsC,qBAAqB,CAACF;IAEjC;IAEA,IAAIF,YAAYb,MAAM,GAAG,GAAG;QAC1BH,SAASA,OAAOI,MAAM,CAAC;YACrB,CAAC,EAAEzB,KAAK,aAAa,CAAC;YACtBgB,WAAWqB;YACX;SACD;IACH;IAEA,OAAOhB,OAAOH,IAAI,CAAC;AACrB;AAEA,SAASjB,mBAAmB,GAAE"}