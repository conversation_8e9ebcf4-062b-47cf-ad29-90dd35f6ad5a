{"version": 3, "sources": ["../../../src/lib/memory/trace.ts"], "names": ["v8", "info", "warn", "trace", "bold", "italic", "join", "traceGlobals", "HEAP_SNAPSHOT_THRESHOLD_PERCENT", "alreadyGeneratedHeapSnapshot", "TRACE_MEMORY_USAGE_TIMER_MS", "traceMemoryUsageTimer", "allMemoryUsage", "startPeriodicMemoryUsageTracing", "setTimeout", "traceMemoryUsage", "stopPeriodicMemoryUsageTracing", "clearTimeout", "getAllMemoryUsageSpans", "description", "parentSpan", "memoryUsage", "process", "v8HeapStatistics", "getHeapStatistics", "heapUsed", "used_heap_size", "heapMax", "heap_size_limit", "tracedMemoryUsage", "rss", "heapTotal", "push", "tracedMemoryUsageAsStrings", "Object", "fromEntries", "entries", "map", "key", "value", "String", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "env", "EXPERIMENTAL_DEBUG_MEMORY_USAGE", "percentageHeapUsed", "toFixed", "distDir", "get", "heapFilename", "replace", "writeHeapSnapshot"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,SAASC,IAAI,EAAEC,IAAI,QAAQ,yBAAwB;AACnD,SAAoBC,KAAK,QAAQ,cAAa;AAC9C,SAASC,IAAI,EAAEC,MAAM,QAAQ,gBAAe;AAC5C,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,YAAY,QAAQ,qBAAoB;AAEjD,MAAMC,kCAAkC;AACxC,IAAIC,+BAA+B;AAEnC,MAAMC,8BAA8B;AACpC,IAAIC;AASJ,MAAMC,iBAAgC,EAAE;AAExC;;;CAGC,GACD,OAAO,SAASC;IACdF,wBAAwBG,WAAW;QACjCC,iBAAiB;QACjBF;IACF,GAAGH;AACL;AAEA,OAAO,SAASM;IACd,IAAIL,uBAAuB;QACzBM,aAAaN;IACf;AACF;AAEA;;CAEC,GACD,OAAO,SAASO;IACd,OAAON;AACT;AAEA;;;CAGC,GACD,OAAO,SAASG,iBACdI,WAAmB,EACnBC,UAA6B;IAE7B,MAAMC,cAAcC,QAAQD,WAAW;IACvC,MAAME,mBAAmBvB,GAAGwB,iBAAiB;IAC7C,MAAMC,WAAWF,iBAAiBG,cAAc;IAChD,MAAMC,UAAUJ,iBAAiBK,eAAe;IAChD,MAAMC,oBAAiC;QACrC,cAAcR,YAAYS,GAAG;QAC7B,mBAAmBL;QACnB,oBAAoBJ,YAAYU,SAAS;QACzC,kBAAkBJ;IACpB;IACAf,eAAeoB,IAAI,CAACH;IACpB,MAAMI,6BAA6BC,OAAOC,WAAW,CACnDD,OAAOE,OAAO,CAACP,mBAAmBQ,GAAG,CAAC,CAAC,CAACC,KAAKC,MAAM,GAAK;YACtDD;YACAE,OAAOD;SACR;IAEH,IAAInB,YAAY;QACdA,WAAWqB,UAAU,CAAC,gBAAgBR;IACxC,OAAO;QACL9B,MAAM,gBAAgBuC,WAAWT;IACnC;IACA,IAAIX,QAAQqB,GAAG,CAACC,+BAA+B,EAAE;QAC/C,MAAMC,qBAAqB,AAAC,MAAMpB,WAAYE;QAE9C1B,KAAK;QACLA,KAAK;QACLA,KAAK,CAAC,wBAAwB,EAAEkB,YAAY,EAAE,CAAC;QAC/ClB,KAAK,CAAC,QAAQ,EAAE,AAACoB,CAAAA,YAAYS,GAAG,GAAG,OAAO,IAAG,EAAGgB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC/D7C,KAAK,CAAC,cAAc,EAAE,AAACwB,CAAAA,WAAW,OAAO,IAAG,EAAGqB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC9D7C,KACE,CAAC,yBAAyB,EAAE,AAACoB,CAAAA,YAAYU,SAAS,GAAG,OAAO,IAAG,EAAGe,OAAO,CACvE,GACA,GAAG,CAAC;QAER7C,KAAK,CAAC,aAAa,EAAE,AAAC0B,CAAAA,UAAU,OAAO,IAAG,EAAGmB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC5D7C,KAAK,CAAC,yBAAyB,EAAE4C,mBAAmBC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjE7C,KAAK;QACLA,KAAK;QAEL,IAAI4C,qBAAqBrC,iCAAiC;YACxD,MAAMuC,UAAUxC,aAAayC,GAAG,CAAC;YACjC,MAAMC,eAAe3C,KACnByC,SACA,CAAC,EAAE5B,YAAY+B,OAAO,CAAC,KAAK,KAAK,aAAa,CAAC;YAEjDhD,KACEE,KACE,CAAC,kCAAkC,EAAEyC,mBAAmBC,OAAO,CAC7D,GACA,wBAAwB,CAAC;YAG/B,IAAI,CAACrC,8BAA8B;gBACjCP,KACEE,KACE,CAAC,wBAAwB,EAAE6C,aAAa,GAAG,EAAE5C,OAC3C,mCACA,CAAC;gBAGPL,GAAGmD,iBAAiB,CAACF;gBACrBxC,+BAA+B;YACjC,OAAO;gBACLP,KACE;YAEJ;QACF;IACF;AACF"}