{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-opengraph.ts"], "names": ["resolveAsArrayOrUndefined", "getSocialImageFallbackMetadataBase", "isStringOrURL", "resolveUrl", "resolveAbsoluteUrlWithPathname", "resolveTitle", "isFullStringUrl", "warnOnce", "Og<PERSON><PERSON><PERSON><PERSON>s", "article", "song", "playlist", "radio", "video", "basic", "resolveAndValidateImage", "item", "metadataBase", "isMetadataBaseMissing", "isStandaloneMode", "undefined", "isItemUrl", "inputUrl", "url", "isNonVercelDeployment", "process", "env", "VERCEL", "NODE_ENV", "validateResolvedImageUrl", "resolveImages", "images", "resolvedImages", "fallbackMetadataBase", "nonNullableImages", "resolvedItem", "push", "ogTypeToFields", "book", "getFieldsByOgType", "ogType", "concat", "origin", "resolveOpenGraph", "openGraph", "metadataContext", "titleTemplate", "resolveProps", "target", "og", "type", "keys", "k", "key", "value", "arrayValue", "resolved", "title", "TwitterBasicInfoKeys", "resolveTwitter", "twitter", "card", "infoKey", "length", "players", "app"], "mappings": "AAWA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SACEC,kCAAkC,EAClCC,aAAa,EACbC,UAAU,EACVC,8BAA8B,QACzB,gBAAe;AACtB,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,eAAe,QAAQ,YAAW;AAC3C,SAASC,QAAQ,QAAQ,4BAA2B;AAKpD,MAAMC,eAAe;IACnBC,SAAS;QAAC;QAAW;KAAO;IAC5BC,MAAM;QAAC;QAAU;KAAY;IAC7BC,UAAU;QAAC;QAAU;KAAY;IACjCC,OAAO;QAAC;KAAW;IACnBC,OAAO;QAAC;QAAU;QAAa;QAAW;KAAO;IACjDC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,SAASC,wBACPC,IAA2D,EAC3DC,YAA+C,EAC/CC,qBAA8B,EAC9BC,gBAAyB;IAEzB,IAAI,CAACH,MAAM,OAAOI;IAClB,MAAMC,YAAYnB,cAAcc;IAChC,MAAMM,WAAWD,YAAYL,OAAOA,KAAKO,GAAG;IAC5C,IAAI,CAACD,UAAU,OAAOF;IAEtB,MAAMI,wBACJ,CAACC,QAAQC,GAAG,CAACC,MAAM,IAAIF,QAAQC,GAAG,CAACE,QAAQ,KAAK;IAClD,qEAAqE;IACrE,IAAIT,oBAAoBK,uBAAuB;QAC7CK,yBAAyBP,UAAUL,cAAcC;IACnD;IAEA,OAAOG,YACH;QACEE,KAAKpB,WAAWmB,UAAUL;IAC5B,IACA;QACE,GAAGD,IAAI;QACP,8BAA8B;QAC9BO,KAAKpB,WAAWmB,UAAUL;IAC5B;AACN;AAYA,OAAO,SAASa,cACdC,MAA+C,EAC/Cd,YAAkC,EAClCE,gBAAyB;IAIzB,MAAMa,iBAAiBhC,0BAA0B+B;IACjD,IAAI,CAACC,gBAAgB,OAAOA;IAE5B,MAAM,EAAEd,qBAAqB,EAAEe,oBAAoB,EAAE,GACnDhC,mCAAmCgB;IACrC,MAAMiB,oBAAoB,EAAE;IAC5B,KAAK,MAAMlB,QAAQgB,eAAgB;QACjC,MAAMG,eAAepB,wBACnBC,MACAiB,sBACAf,uBACAC;QAEF,IAAI,CAACgB,cAAc;QAEnBD,kBAAkBE,IAAI,CAACD;IACzB;IAEA,OAAOD;AACT;AAEA,MAAMG,iBAAoD;IACxD5B,SAASD,aAAaC,OAAO;IAC7B6B,MAAM9B,aAAaC,OAAO;IAC1B,cAAcD,aAAaE,IAAI;IAC/B,eAAeF,aAAaE,IAAI;IAChC,kBAAkBF,aAAaG,QAAQ;IACvC,uBAAuBH,aAAaI,KAAK;IACzC,eAAeJ,aAAaK,KAAK;IACjC,iBAAiBL,aAAaK,KAAK;AACrC;AAEA,SAAS0B,kBAAkBC,MAAiC;IAC1D,IAAI,CAACA,UAAU,CAAEA,CAAAA,UAAUH,cAAa,GAAI,OAAO7B,aAAaM,KAAK;IACrE,OAAOuB,cAAc,CAACG,OAAO,CAACC,MAAM,CAACjC,aAAaM,KAAK;AACzD;AAEA,SAASe,yBACPP,QAAsB,EACtBW,oBAAuD,EACvDf,qBAA8B;IAE9B,yEAAyE;IACzE,IACE,OAAOI,aAAa,YACpB,CAAChB,gBAAgBgB,aACjBJ,uBACA;QACAX,SACE,CAAC,8GAA8G,EAAE0B,qBAAqBS,MAAM,CAAC,yFAAyF,CAAC;IAE3O;AACF;AAEA,OAAO,MAAMC,mBAGT,CAACC,WAAW3B,cAAc4B,iBAAiBC;IAC7C,IAAI,CAACF,WAAW,OAAO;IAEvB,SAASG,aAAaC,MAAyB,EAAEC,EAAa;QAC5D,MAAMT,SAASS,MAAM,UAAUA,KAAKA,GAAGC,IAAI,GAAG9B;QAC9C,MAAM+B,OAAOZ,kBAAkBC;QAC/B,KAAK,MAAMY,KAAKD,KAAM;YACpB,MAAME,MAAMD;YACZ,IAAIC,OAAOJ,MAAMI,QAAQ,OAAO;gBAC9B,MAAMC,QAAQL,EAAE,CAACI,IAAI;gBACrB,IAAIC,OAAO;oBACT,MAAMC,aAAavD,0BAA0BsD;oBAE3CN,MAAc,CAACK,IAAI,GAAGE;gBAC1B;YACF;QACF;QACAP,OAAOjB,MAAM,GAAGD,cACdmB,GAAGlB,MAAM,EACTd,cACA4B,gBAAgB1B,gBAAgB;IAEpC;IAEA,MAAMqC,WAAW;QACf,GAAGZ,SAAS;QACZa,OAAOpD,aAAauC,UAAUa,KAAK,EAAEX;IACvC;IACAC,aAAaS,UAAUZ;IAEvBY,SAASjC,GAAG,GAAGqB,UAAUrB,GAAG,GACxBnB,+BACEwC,UAAUrB,GAAG,EACbN,cACA4B,mBAEF;IAEJ,OAAOW;AACT,EAAC;AAED,MAAME,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,MAAMC,iBAGT,CAACC,SAAS3C,cAAc4B,iBAAiBC;QAiB3BU;IAhBhB,IAAI,CAACI,SAAS,OAAO;IACrB,IAAIC,OAAO,UAAUD,UAAUA,QAAQC,IAAI,GAAGzC;IAC9C,MAAMoC,WAAW;QACf,GAAGI,OAAO;QACVH,OAAOpD,aAAauD,QAAQH,KAAK,EAAEX;IACrC;IACA,KAAK,MAAMgB,WAAWJ,qBAAsB;QAC1CF,QAAQ,CAACM,QAAQ,GAAGF,OAAO,CAACE,QAAQ,IAAI;IAC1C;IAEAN,SAASzB,MAAM,GAAGD,cAChB8B,QAAQ7B,MAAM,EACdd,cACA4B,gBAAgB1B,gBAAgB;IAGlC0C,OAAOA,QAASL,CAAAA,EAAAA,mBAAAA,SAASzB,MAAM,qBAAfyB,iBAAiBO,MAAM,IAAG,wBAAwB,SAAQ;IAC1EP,SAASK,IAAI,GAAGA;IAEhB,IAAI,UAAUL,UAAU;QACtB,OAAQA,SAASK,IAAI;YACnB,KAAK;gBAAU;oBACbL,SAASQ,OAAO,GAAGhE,0BAA0BwD,SAASQ,OAAO,KAAK,EAAE;oBACpE;gBACF;YACA,KAAK;gBAAO;oBACVR,SAASS,GAAG,GAAGT,SAASS,GAAG,IAAI,CAAC;oBAChC;gBACF;YACA;gBACE;QACJ;IACF;IAEA,OAAOT;AACT,EAAC"}