{"version": 3, "sources": ["../../../src/server/lib/dev-bundler-service.ts"], "names": ["DevBundlerService", "constructor", "bundler", "handler", "ensurePage", "definition", "hotReloader", "logErrorWithOriginalStack", "args", "getFallbackErrorComponents", "url", "buildFallbackError", "page", "clientOnly", "undefined", "getCompilationError", "errors", "getCompilationErrors", "revalidate", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "revalidateOpts", "mocked", "createRequestResponseMocks", "headers", "req", "res", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "unstable_onlyGenerated", "Error"], "mappings": ";;;;+BAUaA;;;eAAAA;;;6BAN8B;AAMpC,MAAMA;IACXC,YACE,AAAiBC,OAAmB,EACpC,AAAiBC,OAA6B,CAC9C;aAFiBD,UAAAA;aACAC,UAAAA;aAGZC,aAAyD,OAC9DC;YAEA,oDAAoD;YACpD,OAAO,MAAM,IAAI,CAACH,OAAO,CAACI,WAAW,CAACF,UAAU,CAACC;QACnD;aAEOE,4BACL,OAAO,GAAGC;YACR,OAAO,MAAM,IAAI,CAACN,OAAO,CAACK,yBAAyB,IAAIC;QACzD;IAZC;IAcH,MAAaC,2BAA2BC,GAAY,EAAE;QACpD,MAAM,IAAI,CAACR,OAAO,CAACI,WAAW,CAACK,kBAAkB;QACjD,4DAA4D;QAC5D,8DAA8D;QAC9D,MAAM,IAAI,CAACT,OAAO,CAACI,WAAW,CAACF,UAAU,CAAC;YACxCQ,MAAM;YACNC,YAAY;YACZR,YAAYS;YACZJ;QACF;IACF;IAEA,MAAaK,oBAAoBH,IAAY,EAAE;QAC7C,MAAMI,SAAS,MAAM,IAAI,CAACd,OAAO,CAACI,WAAW,CAACW,oBAAoB,CAACL;QACnE,IAAI,CAACI,QAAQ;QAEb,wCAAwC;QACxC,OAAOA,MAAM,CAAC,EAAE;IAClB;IAEA,MAAaE,WAAW,EACtBC,OAAO,EACPC,iBAAiB,EACjBC,MAAMC,cAAc,EAKrB,EAAE;QACD,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxCd,KAAKS;YACLM,SAASL;QACX;QAEA,MAAM,IAAI,CAACjB,OAAO,CAACoB,OAAOG,GAAG,EAAEH,OAAOI,GAAG;QACzC,MAAMJ,OAAOI,GAAG,CAACC,WAAW;QAE5B,IACEL,OAAOI,GAAG,CAACE,SAAS,CAAC,sBAAsB,iBAC3C,CAAEN,CAAAA,OAAOI,GAAG,CAACG,UAAU,KAAK,OAAOR,eAAeS,sBAAsB,AAAD,GACvE;YACA,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAET,OAAOI,GAAG,CAACG,UAAU,CAAC,CAAC;QAC7D;QAEA,OAAO,CAAC;IACV;AACF"}