{"version": 3, "sources": ["../../../../../src/server/lib/squoosh/png/squoosh_oxipng.js"], "names": ["cleanup", "optimise", "wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "decode", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "getStringFromWasm0", "ptr", "len", "subarray", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "length", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "getArrayU8FromWasm0", "data", "level", "interlace", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "wbg", "__wbindgen_throw", "arg0", "arg1", "Error", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": ";;;;;;;;;;;;;;;;IAkHgBA,OAAO;eAAPA;;IAHhB,OAAmB;eAAnB;;IA1DgBC,QAAQ;eAARA;;;AArDhB,IAAIC;AAEJ,IAAIC,oBAAoB,IAAIC,YAAY,SAAS;IAC/CC,WAAW;IACXC,OAAO;AACT;AAEAH,kBAAkBI,MAAM;AAExB,IAAIC,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBE,MAAM,KAAKR,KAAKS,MAAM,CAACD,MAAM,EAClD;QACAF,uBAAuB,IAAII,WAAWV,KAAKS,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOF;AACT;AAEA,SAASK,mBAAmBC,GAAG,EAAEC,GAAG;IAClC,OAAOZ,kBAAkBI,MAAM,CAACE,kBAAkBO,QAAQ,CAACF,KAAKA,MAAMC;AACxE;AAEA,IAAIE,kBAAkB;AAEtB,SAASC,kBAAkBC,GAAG,EAAEC,MAAM;IACpC,MAAMN,MAAMM,OAAOD,IAAIE,MAAM,GAAG;IAChCZ,kBAAkBa,GAAG,CAACH,KAAKL,MAAM;IACjCG,kBAAkBE,IAAIE,MAAM;IAC5B,OAAOP;AACT;AAEA,IAAIS,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBb,MAAM,KAAKR,KAAKS,MAAM,CAACD,MAAM,EAClD;QACAa,uBAAuB,IAAIE,WAAWvB,KAAKS,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOa;AACT;AAEA,SAASG,oBAAoBZ,GAAG,EAAEC,GAAG;IACnC,OAAON,kBAAkBO,QAAQ,CAACF,MAAM,GAAGA,MAAM,IAAIC;AACvD;AAOO,SAASd,SAAS0B,IAAI,EAAEC,KAAK,EAAEC,SAAS;IAC7C,IAAI;QACF,MAAMC,SAAS5B,KAAK6B,+BAA+B,CAAC,CAAC;QACrD,IAAIC,OAAOd,kBAAkBS,MAAMzB,KAAK+B,iBAAiB;QACzD,IAAIC,OAAOjB;QACXf,KAAKD,QAAQ,CAAC6B,QAAQE,MAAME,MAAMN,OAAOC;QACzC,IAAIM,KAAKX,iBAAiB,CAACM,SAAS,IAAI,EAAE;QAC1C,IAAIM,KAAKZ,iBAAiB,CAACM,SAAS,IAAI,EAAE;QAC1C,IAAIO,KAAKX,oBAAoBS,IAAIC,IAAIE,KAAK;QAC1CpC,KAAKqC,eAAe,CAACJ,IAAIC,KAAK;QAC9B,OAAOC;IACT,SAAU;QACRnC,KAAK6B,+BAA+B,CAAC;IACvC;AACF;AAEA,eAAeS,KAAKC,OAAM,EAAEC,OAAO;IACjC,IAAI,OAAOC,aAAa,cAAcF,mBAAkBE,UAAU;QAChE,IAAI,OAAOC,YAAYC,oBAAoB,KAAK,YAAY;YAC1D,OAAO,MAAMD,YAAYC,oBAAoB,CAACJ,SAAQC;QACxD;QAEA,MAAMI,QAAQ,MAAML,QAAOM,WAAW;QACtC,OAAO,MAAMH,YAAYI,WAAW,CAACF,OAAOJ;IAC9C,OAAO;QACL,MAAMO,WAAW,MAAML,YAAYI,WAAW,CAACP,SAAQC;QAEvD,IAAIO,oBAAoBL,YAAYM,QAAQ,EAAE;YAC5C,OAAO;gBAAED;gBAAUR,QAAAA;YAAO;QAC5B,OAAO;YACL,OAAOQ;QACT;IACF;AACF;AAEA,eAAeE,KAAKC,KAAK;IACvB,MAAMV,UAAU,CAAC;IACjBA,QAAQW,GAAG,GAAG,CAAC;IACfX,QAAQW,GAAG,CAACC,gBAAgB,GAAG,SAAUC,IAAI,EAAEC,IAAI;QACjD,MAAM,IAAIC,MAAM5C,mBAAmB0C,MAAMC;IAC3C;IAEA,IACE,OAAOJ,UAAU,YAChB,OAAOM,YAAY,cAAcN,iBAAiBM,WAClD,OAAOC,QAAQ,cAAcP,iBAAiBO,KAC/C;QACAP,QAAQQ,MAAMR;IAChB;IAEA,MAAM,EAAEH,QAAQ,EAAER,QAAAA,OAAM,EAAE,GAAG,MAAMD,KAAK,MAAMY,OAAOV;IAErDxC,OAAO+C,SAASY,OAAO;IACvBV,KAAKW,sBAAsB,GAAGrB;IAE9B,OAAOvC;AACT;MAEA,WAAeiD;AAGR,SAASnD;IACdE,OAAO;IACPM,uBAAuB;IACvBe,uBAAuB;AACzB"}