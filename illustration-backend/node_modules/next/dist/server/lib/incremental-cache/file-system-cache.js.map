{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "names": ["FileSystemCache", "memoryCache", "tagsManifest", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "appDir", "_appDir", "pagesDir", "_pagesDir", "revalidatedTags", "experimental", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "maxMemoryCacheSize", "console", "log", "L<PERSON><PERSON><PERSON>", "max", "length", "value", "JSON", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "tagsManifestPath", "path", "join", "loadTagsManifest", "resetRequestCache", "parse", "readFileSync", "err", "version", "items", "revalidateTag", "args", "tags", "tag", "revalidatedAt", "Date", "now", "mkdir", "dirname", "writeFile", "warn", "get", "key", "softTags", "kindHint", "NEXT_RUNTIME", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "replace", "NEXT_META_SUFFIX", "cacheEntry", "lastModified", "getTime", "headers", "status", "_", "detectFileKind", "isAppPath", "parsedData", "storedTags", "every", "includes", "set", "ppr", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "NEXT_DATA_SUFFIX", "postponed", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_CACHE_TAGS_HEADER", "split", "isStale", "some", "undefined", "combinedTags", "wasRevalidated", "htmlPath", "pathname", "existsSync"], "mappings": ";;;;+BA+BA;;;eAAqBA;;;iEA1BA;6DACJ;2BAOV;;;;;;AAeP,IAAIC;AACJ,IAAIC;AAEW,MAAMF;IAWnBG,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,MAAM,GAAG,CAAC,CAACJ,IAAIK,OAAO;QAC3B,IAAI,CAACC,QAAQ,GAAG,CAAC,CAACN,IAAIO,SAAS;QAC/B,IAAI,CAACC,eAAe,GAAGR,IAAIQ,eAAe;QAC1C,IAAI,CAACC,YAAY,GAAGT,IAAIS,YAAY;QACpC,IAAI,CAACC,KAAK,GAAG,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QAEnD,IAAIb,IAAIc,kBAAkB,IAAI,CAACjB,aAAa;YAC1C,IAAI,IAAI,CAACa,KAAK,EAAE;gBACdK,QAAQC,GAAG,CAAC;YACd;YAEAnB,cAAc,IAAIoB,iBAAQ,CAAC;gBACzBC,KAAKlB,IAAIc,kBAAkB;gBAC3BK,QAAO,EAAEC,KAAK,EAAE;wBAcSC;oBAbvB,IAAI,CAACD,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAME,IAAI,KAAK,YAAY;wBACpC,OAAOD,KAAKE,SAAS,CAACH,MAAMI,KAAK,EAAEL,MAAM;oBAC3C,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,MAAM,IAAIG,MAAM;oBAClB,OAAO,IAAIL,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOD,KAAKE,SAAS,CAACH,MAAMM,IAAI,IAAI,IAAIP,MAAM;oBAChD,OAAO,IAAIC,MAAME,IAAI,KAAK,SAAS;wBACjC,OAAOF,MAAMO,IAAI,CAACR,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEC,MAAMQ,IAAI,CAACT,MAAM,GAAIE,CAAAA,EAAAA,kBAAAA,KAAKE,SAAS,CAACH,MAAMS,QAAQ,sBAA7BR,gBAAgCF,MAAM,KAAI,CAAA;gBAEnE;YACF;QACF,OAAO,IAAI,IAAI,CAACT,KAAK,EAAE;YACrBK,QAAQC,GAAG,CAAC;QACd;QAEA,IAAI,IAAI,CAACb,aAAa,IAAI,IAAI,CAACF,EAAE,EAAE;YACjC,IAAI,CAAC6B,gBAAgB,GAAGC,aAAI,CAACC,IAAI,CAC/B,IAAI,CAAC7B,aAAa,EAClB,MACA,SACA,eACA;YAEF,IAAI,CAAC8B,gBAAgB;QACvB;IACF;IAEOC,oBAA0B,CAAC;IAE1BD,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAACH,gBAAgB,IAAI,CAAC,IAAI,CAAC7B,EAAE,IAAIH,cAAc;QACxD,IAAI;YACFA,eAAeuB,KAAKc,KAAK,CACvB,IAAI,CAAClC,EAAE,CAACmC,YAAY,CAAC,IAAI,CAACN,gBAAgB,EAAE;QAEhD,EAAE,OAAOO,KAAU;YACjBvC,eAAe;gBAAEwC,SAAS;gBAAGC,OAAO,CAAC;YAAE;QACzC;QACA,IAAI,IAAI,CAAC7B,KAAK,EAAEK,QAAQC,GAAG,CAAC,oBAAoBlB;IAClD;IAEA,MAAa0C,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAE3C,IAAI,IAAI,CAAChC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,iBAAiB0B;QAC/B;QAEA,IAAIA,KAAKvB,MAAM,KAAK,GAAG;YACrB;QACF;QAEA,kDAAkD;QAClD,wDAAwD;QACxD,2CAA2C;QAC3C,MAAM,IAAI,CAACc,gBAAgB;QAC3B,IAAI,CAACnC,gBAAgB,CAAC,IAAI,CAACgC,gBAAgB,EAAE;YAC3C;QACF;QAEA,KAAK,MAAMa,OAAOD,KAAM;YACtB,MAAMhB,OAAO5B,aAAayC,KAAK,CAACI,IAAI,IAAI,CAAC;YACzCjB,KAAKkB,aAAa,GAAGC,KAAKC,GAAG;YAC7BhD,aAAayC,KAAK,CAACI,IAAI,GAAGjB;QAC5B;QAEA,IAAI;YACF,MAAM,IAAI,CAACzB,EAAE,CAAC8C,KAAK,CAAChB,aAAI,CAACiB,OAAO,CAAC,IAAI,CAAClB,gBAAgB;YACtD,MAAM,IAAI,CAAC7B,EAAE,CAACgD,SAAS,CACrB,IAAI,CAACnB,gBAAgB,EACrBT,KAAKE,SAAS,CAACzB,gBAAgB,CAAC;YAElC,IAAI,IAAI,CAACY,KAAK,EAAE;gBACdK,QAAQC,GAAG,CAAC,yBAAyBlB;YACvC;QACF,EAAE,OAAOuC,KAAU;YACjBtB,QAAQmC,IAAI,CAAC,mCAAmCb;QAClD;IACF;IAEA,MAAac,IAAI,GAAGV,IAAqC,EAAE;YA8HrDf,aA4BQA;QAzJZ,MAAM,CAAC0B,KAAKpD,MAAM,CAAC,CAAC,CAAC,GAAGyC;QACxB,MAAM,EAAEC,IAAI,EAAEW,QAAQ,EAAEC,QAAQ,EAAE,GAAGtD;QACrC,IAAI0B,OAAO7B,+BAAAA,YAAasD,GAAG,CAACC;QAE5B,IAAI,IAAI,CAAC1C,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAOoC,KAAKV,MAAMY,UAAU,CAAC,CAAC5B;QAC5C;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQf,QAAQC,GAAG,CAAC2C,YAAY,KAAK,QAAQ;YAChD,IAAI;gBACF,MAAMC,WAAW,IAAI,CAACC,WAAW,CAAC,CAAC,EAAEL,IAAI,KAAK,CAAC,EAAE;gBACjD,MAAMM,WAAW,MAAM,IAAI,CAACzD,EAAE,CAAC0D,QAAQ,CAACH;gBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC3D,EAAE,CAAC4D,IAAI,CAACL;gBAErC,MAAMM,OAAOzC,KAAKc,KAAK,CACrB,MAAM,IAAI,CAAClC,EAAE,CAAC0D,QAAQ,CACpBH,SAASO,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;gBAIJ,MAAMC,aAAgC;oBACpCC,cAAcN,MAAMO,OAAO;oBAC3B/C,OAAO;wBACLE,MAAM;wBACNK,MAAM+B;wBACNU,SAASN,KAAKM,OAAO;wBACrBC,QAAQP,KAAKO,MAAM;oBACrB;gBACF;gBACA,OAAOJ;YACT,EAAE,OAAOK,GAAG;YACV,oCAAoC;YACtC;YAEA,IAAI;gBACF,wDAAwD;gBACxD,IAAIhD,OAAOgC;gBACX,IAAI,CAAChC,MAAM;oBACTA,OAAO,IAAI,CAACiD,cAAc,CAAC,CAAC,EAAEnB,IAAI,KAAK,CAAC;gBAC1C;gBAEA,MAAMoB,YAAYlD,SAAS;gBAC3B,MAAMkC,WAAW,IAAI,CAACC,WAAW,CAC/BnC,SAAS,UAAU8B,MAAM,CAAC,EAAEA,IAAI,KAAK,CAAC,EACtC9B;gBAGF,MAAMoC,WAAW,MAAM,IAAI,CAACzD,EAAE,CAAC0D,QAAQ,CAACH,UAAU;gBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC3D,EAAE,CAAC4D,IAAI,CAACL;gBAErC,IAAIlC,SAAS,WAAW,IAAI,CAACpB,WAAW,EAAE;wBAQpCwB;oBAPJ,MAAMwC,eAAeN,MAAMO,OAAO;oBAClC,MAAMM,aAA+BpD,KAAKc,KAAK,CAACuB;oBAChDhC,OAAO;wBACLwC;wBACA9C,OAAOqD;oBACT;oBAEA,IAAI/C,EAAAA,eAAAA,KAAKN,KAAK,qBAAVM,aAAYJ,IAAI,MAAK,SAAS;4BACbI;wBAAnB,MAAMgD,cAAahD,eAAAA,KAAKN,KAAK,qBAAVM,aAAYgB,IAAI;wBAEnC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAMiC,KAAK,CAAC,CAAChC,MAAQ+B,8BAAAA,WAAYE,QAAQ,CAACjC,QAAO;4BACpD,IAAI,IAAI,CAACjC,KAAK,EAAE;gCACdK,QAAQC,GAAG,CAAC,+BAA+B0B,MAAMgC;4BACnD;4BACA,MAAM,IAAI,CAACG,GAAG,CAACzB,KAAK1B,KAAKN,KAAK,EAAE;gCAAEsB;4BAAK;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAMb,WAAW2C,YACb,MAAM,IAAI,CAACvE,EAAE,CAAC0D,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,CAAC,EAAEL,IAAI,EACL,IAAI,CAAC3C,YAAY,CAACqE,GAAG,GAAGC,8BAAmB,GAAGC,qBAAU,CACzD,CAAC,EACF,QAEF,UAEF3D,KAAKc,KAAK,CACR,MAAM,IAAI,CAAClC,EAAE,CAAC0D,QAAQ,CACpB,IAAI,CAACF,WAAW,CAAC,CAAC,EAAEL,IAAI,EAAE6B,2BAAgB,CAAC,CAAC,EAAE,UAC9C;oBAIR,IAAInB;oBAEJ,IAAIU,WAAW;wBACb,IAAI;4BACFV,OAAOzC,KAAKc,KAAK,CACf,MAAM,IAAI,CAAClC,EAAE,CAAC0D,QAAQ,CACpBH,SAASO,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;wBAGN,EAAE,OAAM,CAAC;oBACX;oBAEAtC,OAAO;wBACLwC,cAAcN,MAAMO,OAAO;wBAC3B/C,OAAO;4BACLE,MAAM;4BACNM,MAAM8B;4BACN7B;4BACAqD,SAAS,EAAEpB,wBAAAA,KAAMoB,SAAS;4BAC1Bd,OAAO,EAAEN,wBAAAA,KAAMM,OAAO;4BACtBC,MAAM,EAAEP,wBAAAA,KAAMO,MAAM;wBACtB;oBACF;gBACF;gBAEA,IAAI3C,MAAM;oBACR7B,+BAAAA,YAAagF,GAAG,CAACzB,KAAK1B;gBACxB;YACF,EAAE,OAAO4C,GAAG;YACV,+BAA+B;YACjC;QACF;QAEA,IAAI5C,CAAAA,yBAAAA,cAAAA,KAAMN,KAAK,qBAAXM,YAAaJ,IAAI,MAAK,QAAQ;gBAEbI;YADnB,IAAIyD;YACJ,MAAMC,cAAa1D,sBAAAA,KAAKN,KAAK,CAACgD,OAAO,qBAAlB1C,mBAAoB,CAAC2D,iCAAsB,CAAC;YAE/D,IAAI,OAAOD,eAAe,UAAU;gBAClCD,YAAYC,WAAWE,KAAK,CAAC;YAC/B;YAEA,IAAIH,6BAAAA,UAAWhE,MAAM,EAAE;gBACrB,IAAI,CAACc,gBAAgB;gBAErB,MAAMsD,UAAUJ,UAAUK,IAAI,CAAC,CAAC7C;wBAE5B7C;oBADF,OACEA,CAAAA,iCAAAA,0BAAAA,aAAcyC,KAAK,CAACI,IAAI,qBAAxB7C,wBAA0B8C,aAAa,KACvC9C,CAAAA,gCAAAA,aAAcyC,KAAK,CAACI,IAAI,CAACC,aAAa,KACnClB,CAAAA,CAAAA,wBAAAA,KAAMwC,YAAY,KAAIrB,KAAKC,GAAG,EAAC;gBAEtC;gBAEA,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIyC,SAAS;oBACX7D,OAAO+D;gBACT;YACF;QACF;QAEA,IAAI/D,QAAQA,CAAAA,yBAAAA,eAAAA,KAAMN,KAAK,qBAAXM,aAAaJ,IAAI,MAAK,SAAS;YACzC,IAAI,CAACW,gBAAgB;YAErB,MAAMyD,eAAe;mBAAKhD,QAAQ,EAAE;mBAAOW,YAAY,EAAE;aAAE;YAE3D,MAAMsC,iBAAiBD,aAAaF,IAAI,CAAC,CAAC7C;oBAMtC7C;gBALF,IAAI,IAAI,CAACU,eAAe,CAACoE,QAAQ,CAACjC,MAAM;oBACtC,OAAO;gBACT;gBAEA,OACE7C,CAAAA,iCAAAA,0BAAAA,aAAcyC,KAAK,CAACI,IAAI,qBAAxB7C,wBAA0B8C,aAAa,KACvC9C,CAAAA,gCAAAA,aAAcyC,KAAK,CAACI,IAAI,CAACC,aAAa,KACnClB,CAAAA,CAAAA,wBAAAA,KAAMwC,YAAY,KAAIrB,KAAKC,GAAG,EAAC;YAEtC;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAI6C,gBAAgB;gBAClBjE,OAAO+D;YACT;QACF;QAEA,OAAO/D,QAAQ;IACjB;IAEA,MAAamD,IAAI,GAAGpC,IAAqC,EAAE;QACzD,MAAM,CAACW,KAAK1B,MAAM1B,IAAI,GAAGyC;QACzB5C,+BAAAA,YAAagF,GAAG,CAACzB,KAAK;YACpBhC,OAAOM;YACPwC,cAAcrB,KAAKC,GAAG;QACxB;QACA,IAAI,IAAI,CAACpC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAOoC;QACrB;QAEA,IAAI,CAAC,IAAI,CAAClD,WAAW,EAAE;QAEvB,IAAIwB,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YAC1B,MAAMkC,WAAW,IAAI,CAACC,WAAW,CAAC,CAAC,EAAEL,IAAI,KAAK,CAAC,EAAE;YACjD,MAAM,IAAI,CAACnD,EAAE,CAAC8C,KAAK,CAAChB,aAAI,CAACiB,OAAO,CAACQ;YACjC,MAAM,IAAI,CAACvD,EAAE,CAACgD,SAAS,CAACO,UAAU9B,KAAKC,IAAI;YAE3C,MAAMmC,OAAsB;gBAC1BM,SAAS1C,KAAK0C,OAAO;gBACrBC,QAAQ3C,KAAK2C,MAAM;gBACnBa,WAAWO;YACb;YAEA,MAAM,IAAI,CAACxF,EAAE,CAACgD,SAAS,CACrBO,SAASO,OAAO,CAAC,WAAWC,2BAAgB,GAC5C3C,KAAKE,SAAS,CAACuC,MAAM,MAAM;YAE7B;QACF;QAEA,IAAIpC,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,QAAQ;YACzB,MAAMkD,YAAY,OAAO9C,KAAKG,QAAQ,KAAK;YAC3C,MAAM+D,WAAW,IAAI,CAACnC,WAAW,CAC/B,CAAC,EAAEL,IAAI,KAAK,CAAC,EACboB,YAAY,QAAQ;YAEtB,MAAM,IAAI,CAACvE,EAAE,CAAC8C,KAAK,CAAChB,aAAI,CAACiB,OAAO,CAAC4C;YACjC,MAAM,IAAI,CAAC3F,EAAE,CAACgD,SAAS,CAAC2C,UAAUlE,KAAKE,IAAI;YAE3C,MAAM,IAAI,CAAC3B,EAAE,CAACgD,SAAS,CACrB,IAAI,CAACQ,WAAW,CACd,CAAC,EAAEL,IAAI,EACLoB,YACI,IAAI,CAAC/D,YAAY,CAACqE,GAAG,GACnBC,8BAAmB,GACnBC,qBAAU,GACZC,2BAAgB,CACrB,CAAC,EACFT,YAAY,QAAQ,UAEtBA,YAAY9C,KAAKG,QAAQ,GAAGR,KAAKE,SAAS,CAACG,KAAKG,QAAQ;YAG1D,IAAIH,KAAK0C,OAAO,IAAI1C,KAAK2C,MAAM,EAAE;gBAC/B,MAAMP,OAAsB;oBAC1BM,SAAS1C,KAAK0C,OAAO;oBACrBC,QAAQ3C,KAAK2C,MAAM;oBACnBa,WAAWxD,KAAKwD,SAAS;gBAC3B;gBAEA,MAAM,IAAI,CAACjF,EAAE,CAACgD,SAAS,CACrB2C,SAAS7B,OAAO,CAAC,WAAWC,2BAAgB,GAC5C3C,KAAKE,SAAS,CAACuC;YAEnB;QACF,OAAO,IAAIpC,CAAAA,wBAAAA,KAAMJ,IAAI,MAAK,SAAS;YACjC,MAAMkC,WAAW,IAAI,CAACC,WAAW,CAACL,KAAK;YACvC,MAAM,IAAI,CAACnD,EAAE,CAAC8C,KAAK,CAAChB,aAAI,CAACiB,OAAO,CAACQ;YACjC,MAAM,IAAI,CAACvD,EAAE,CAACgD,SAAS,CACrBO,UACAnC,KAAKE,SAAS,CAAC;gBACb,GAAGG,IAAI;gBACPgB,MAAM1C,IAAI0C,IAAI;YAChB;QAEJ;IACF;IAEQ6B,eAAesB,QAAgB,EAAE;QACvC,IAAI,CAAC,IAAI,CAACzF,MAAM,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;YAClC,MAAM,IAAImB,MACR;QAEJ;QAEA,0EAA0E;QAC1E,OAAO;QACP,IAAI,CAAC,IAAI,CAACrB,MAAM,IAAI,IAAI,CAACE,QAAQ,EAAE;YACjC,OAAO;QACT,OAEK,IAAI,IAAI,CAACF,MAAM,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;YACtC,OAAO;QACT;QAEA,oEAAoE;QACpE,WAAW;QACX,IAAIkD,WAAW,IAAI,CAACC,WAAW,CAACoC,UAAU;QAC1C,IAAI,IAAI,CAAC5F,EAAE,CAAC6F,UAAU,CAACtC,WAAW;YAChC,OAAO;QACT;QAEAA,WAAW,IAAI,CAACC,WAAW,CAACoC,UAAU;QACtC,IAAI,IAAI,CAAC5F,EAAE,CAAC6F,UAAU,CAACtC,WAAW;YAChC,OAAO;QACT;QAEA,MAAM,IAAI/B,MACR,CAAC,kDAAkD,EAAEoE,SAAS,CAAC;IAEnE;IAEQpC,YACNoC,QAAgB,EAChBvE,IAA+B,EACvB;QACR,OAAQA;YACN,KAAK;gBACH,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOS,aAAI,CAACC,IAAI,CACd,IAAI,CAAC7B,aAAa,EAClB,MACA,SACA,eACA0F;YAEJ,KAAK;gBACH,OAAO9D,aAAI,CAACC,IAAI,CAAC,IAAI,CAAC7B,aAAa,EAAE,SAAS0F;YAChD,KAAK;gBACH,OAAO9D,aAAI,CAACC,IAAI,CAAC,IAAI,CAAC7B,aAAa,EAAE,OAAO0F;YAC9C;gBACE,MAAM,IAAIpE,MAAM;QACpB;IACF;AACF"}