{"version": 3, "sources": ["../../../src/server/response-cache/index.ts"], "names": ["ResponseCache", "constructor", "minimalMode", "batcher", "<PERSON><PERSON>", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "scheduleOnNextTick", "minimalModeKey", "get", "responseGenerator", "context", "incrementalCache", "response", "batch", "cache<PERSON>ey", "resolve", "previousCacheItem", "expiresAt", "Date", "now", "entry", "kindHint", "routeKind", "RouteKind", "APP_PAGE", "APP_ROUTE", "PAGES", "resolved", "cachedResponse", "value", "kind", "Error", "revalidate", "curRevalidate", "isStale", "isPrefetch", "cacheEntry", "undefined", "resolveValue", "fromResponseCacheEntry", "isMiss", "set", "err", "Math", "min", "max", "console", "error", "toResponseCacheEntry"], "mappings": ";;;;+BAgBA;;;eAAqBA;;;;2BARK;yBAEF;2BACW;uBAC0B;qBAE/C;;;;;;;;;;;;;;AAEC,MAAMA;IAwBnBC,YAAYC,WAAoB,CAAE;aAvBjBC,UAAUC,gBAAO,CAACC,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,CAAC,EAAED,IAAI,CAAC,EAAEC,uBAAuB,MAAM,IAAI,CAAC;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,aAAaC,6BAAkB;QACjC;QAWE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGT;IACzB;IAEA,MAAaU,IACXL,GAAkB,EAClBM,iBAAoC,EACpCC,OAKC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACP,KAAK,OAAOM,kBAAkB,OAAO;QAE1C,MAAM,EAAEE,gBAAgB,EAAEP,uBAAuB,KAAK,EAAE,GAAGM;QAE3D,MAAME,WAAW,MAAM,IAAI,CAACb,OAAO,CAACc,KAAK,CACvC;YAAEV;YAAKC;QAAqB,GAC5B,OAAOU,UAAUC;gBAKb;YAJF,+DAA+D;YAC/D,iDAAiD;YACjD,IACE,IAAI,CAACjB,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACkB,iBAAiB,qBAAtB,wBAAwBb,GAAG,MAAKW,YAChC,IAAI,CAACE,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;gBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;YACrC;YAEA,mEAAmE;YACnE,IAAIC;YACJ,IACEX,QAAQY,SAAS,KAAKC,oBAAS,CAACC,QAAQ,IACxCd,QAAQY,SAAS,KAAKC,oBAAS,CAACE,SAAS,EACzC;gBACAJ,WAAW;YACb,OAAO,IAAIX,QAAQY,SAAS,KAAKC,oBAAS,CAACG,KAAK,EAAE;gBAChDL,WAAW;YACb;YAEA,IAAIM,WAAW;YACf,IAAIC,iBAAuC;YAC3C,IAAI;gBACFA,iBAAiB,CAAC,IAAI,CAAC9B,WAAW,GAC9B,MAAMa,iBAAiBH,GAAG,CAACL,KAAK;oBAAEkB;gBAAS,KAC3C;gBAEJ,IAAIO,kBAAkB,CAACxB,sBAAsB;wBACvCwB;oBAAJ,IAAIA,EAAAA,wBAAAA,eAAeC,KAAK,qBAApBD,sBAAsBE,IAAI,MAAK,SAAS;wBAC1C,MAAM,IAAIC,MACR,CAAC,oEAAoE,CAAC;oBAE1E;oBAEAhB,QAAQ;wBACN,GAAGa,cAAc;wBACjBI,YAAYJ,eAAeK,aAAa;oBAC1C;oBACAN,WAAW;oBAEX,IAAI,CAACC,eAAeM,OAAO,IAAIxB,QAAQyB,UAAU,EAAE;wBACjD,oDAAoD;wBACpD,oBAAoB;wBACpB,OAAO;oBACT;gBACF;gBAEA,MAAMC,aAAa,MAAM3B,kBACvBkB,UACAC,gBACA;gBAGF,mEAAmE;gBACnE,cAAc;gBACd,IAAI,CAACQ,YAAY;oBACf,+CAA+C;oBAC/C,IAAI,IAAI,CAACtC,WAAW,EAAE,IAAI,CAACkB,iBAAiB,GAAGqB;oBAC/C,OAAO;gBACT;gBAEA,MAAMC,eAAe,MAAMC,IAAAA,6BAAsB,EAAC;oBAChD,GAAGH,UAAU;oBACbI,QAAQ,CAACZ;gBACX;gBACA,IAAI,CAACU,cAAc;oBACjB,+CAA+C;oBAC/C,IAAI,IAAI,CAACxC,WAAW,EAAE,IAAI,CAACkB,iBAAiB,GAAGqB;oBAC/C,OAAO;gBACT;gBAEA,+DAA+D;gBAC/D,yBAAyB;gBACzB,IAAI,CAACjC,wBAAwB,CAACuB,UAAU;oBACtCZ,QAAQuB;oBACRX,WAAW;gBACb;gBAEA,IAAI,OAAOW,aAAaN,UAAU,KAAK,aAAa;oBAClD,IAAI,IAAI,CAAClC,WAAW,EAAE;wBACpB,IAAI,CAACkB,iBAAiB,GAAG;4BACvBb,KAAKW;4BACLM,OAAOkB;4BACPrB,WAAWC,KAAKC,GAAG,KAAK;wBAC1B;oBACF,OAAO;wBACL,MAAMR,iBAAiB8B,GAAG,CAACtC,KAAKmC,aAAaT,KAAK,EAAE;4BAClDG,YAAYM,aAAaN,UAAU;wBACrC;oBACF;gBACF;gBAEA,OAAOM;YACT,EAAE,OAAOI,KAAK;gBACZ,qEAAqE;gBACrE,sEAAsE;gBACtE,IAAId,gBAAgB;oBAClB,MAAMjB,iBAAiB8B,GAAG,CAACtC,KAAKyB,eAAeC,KAAK,EAAE;wBACpDG,YAAYW,KAAKC,GAAG,CAClBD,KAAKE,GAAG,CAACjB,eAAeI,UAAU,IAAI,GAAG,IACzC;oBAEJ;gBACF;gBAEA,qEAAqE;gBACrE,kDAAkD;gBAClD,IAAIL,UAAU;oBACZmB,QAAQC,KAAK,CAACL;oBACd,OAAO;gBACT;gBAEA,gEAAgE;gBAChE,MAAMA;YACR;QACF;QAGF,OAAOM,IAAAA,2BAAoB,EAACpC;IAC9B;AACF"}