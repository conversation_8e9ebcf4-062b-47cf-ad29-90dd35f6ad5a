{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/pages-api-route-matcher-provider.ts"], "names": ["PagesAPIRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "PAGES_MANIFEST", "normalizers", "PagesNormalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "isAPIRoute", "matchers", "page", "detectedLocale", "analyze", "push", "PagesAPILocaleRouteMatcher", "kind", "RouteKind", "PAGES_API", "bundlePath", "normalize", "filename", "i18n", "locale", "PagesAPIRouteMatcher"], "mappings": ";;;;+BAeaA;;;eAAAA;;;4BAfc;2BACI;2BACL;sCAInB;8CAKsC;uBAEZ;AAE1B,MAAMA,qCAAqCC,0DAA4B;IAG5EC,YACEC,OAAe,EACfC,cAA8B,EAC9B,AAAiBC,YAA2B,CAC5C;QACA,KAAK,CAACC,yBAAc,EAAEF;aAFLC,eAAAA;QAIjB,IAAI,CAACE,WAAW,GAAG,IAAIC,uBAAgB,CAACL;IAC1C;IAEA,MAAgBM,UACdC,QAAkB,EAC4B;QAC9C,6CAA6C;QAC7C,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,WAC9CC,IAAAA,sBAAU,EAACD;QAGb,MAAME,WAAwC,EAAE;QAEhD,KAAK,MAAMC,QAAQP,UAAW;YAC5B,IAAI,IAAI,CAACN,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEc,cAAc,EAAEJ,QAAQ,EAAE,GAAG,IAAI,CAACV,YAAY,CAACe,OAAO,CAACF;gBAE/DD,SAASI,IAAI,CACX,IAAIC,gDAA0B,CAAC;oBAC7BC,MAAMC,oBAAS,CAACC,SAAS;oBACzBV;oBACAG;oBACAQ,YAAY,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACC,SAAS,CAACT;oBAClDU,UAAU,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAACD,SAAS,CAACjB,QAAQ,CAACQ,KAAK;oBAC5DW,MAAM;wBACJC,QAAQX;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASI,IAAI,CACX,IAAIU,0CAAoB,CAAC;oBACvBR,MAAMC,oBAAS,CAACC,SAAS;oBACzB,qDAAqD;oBACrDV,UAAUG;oBACVA;oBACAQ,YAAY,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACC,SAAS,CAACT;oBAClDU,UAAU,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAACD,SAAS,CAACjB,QAAQ,CAACQ,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}