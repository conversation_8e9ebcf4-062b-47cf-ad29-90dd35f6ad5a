{"version": 3, "sources": ["../../../src/server/web/globals.ts"], "names": ["ensureInstrumentationRegistered", "registerInstrumentation", "register", "globalThis", "_ENTRIES", "middleware_instrumentation", "err", "message", "registerInstrumentationPromise", "getUnsupportedModuleErrorMessage", "module", "__import_unsupported", "moduleName", "proxy", "Proxy", "get", "_obj", "prop", "Error", "construct", "apply", "_target", "_this", "args", "enhanceGlobals", "process", "global", "env", "Object", "defineProperty", "value", "enumerable", "configurable"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;AAhBhB,eAAeC;IACb,MAAMC,WACJ,cAAcC,cACdC,SAASC,0BAA0B,IACnC,AAAC,CAAA,MAAMD,SAASC,0BAA0B,AAAD,EAAGH,QAAQ;IACtD,IAAIA,UAAU;QACZ,IAAI;YACF,MAAMA;QACR,EAAE,OAAOI,KAAU;YACjBA,IAAIC,OAAO,GAAG,CAAC,sDAAsD,EAAED,IAAIC,OAAO,CAAC,CAAC;YACpF,MAAMD;QACR;IACF;AACF;AAEA,IAAIE,iCAAuD;AACpD,SAASR;IACd,IAAI,CAACQ,gCAAgC;QACnCA,iCAAiCP;IACnC;IACA,OAAOO;AACT;AAEA,SAASC,iCAAiCC,MAAc;IACtD,sHAAsH;IACtH,OAAO,CAAC,2CAA2C,EAAEA,OAAO;wEACU,CAAC;AACzE;AAEA,SAASC,qBAAqBC,UAAkB;IAC9C,MAAMC,QAAa,IAAIC,MAAM,YAAa,GAAG;QAC3CC,KAAIC,IAAI,EAAEC,IAAI;YACZ,IAAIA,SAAS,QAAQ;gBACnB,OAAO,CAAC;YACV;YACA,MAAM,IAAIC,MAAMT,iCAAiCG;QACnD;QACAO;YACE,MAAM,IAAID,MAAMT,iCAAiCG;QACnD;QACAQ,OAAMC,OAAO,EAAEC,KAAK,EAAEC,IAAI;YACxB,IAAI,OAAOA,IAAI,CAAC,EAAE,KAAK,YAAY;gBACjC,OAAOA,IAAI,CAAC,EAAE,CAACV;YACjB;YACA,MAAM,IAAIK,MAAMT,iCAAiCG;QACnD;IACF;IACA,OAAO,IAAIE,MAAM,CAAC,GAAG;QAAEC,KAAK,IAAMF;IAAM;AAC1C;AAEA,SAASW;IACP,8DAA8D;IAC9D,IAAIC,YAAYC,OAAOD,OAAO,EAAE;QAC9B,4DAA4D;QAC5DA,QAAQE,GAAG,GAAGD,OAAOD,OAAO,CAACE,GAAG;QAChCD,OAAOD,OAAO,GAAGA;IACnB;IAEA,uEAAuE;IACvE,6DAA6D;IAC7DG,OAAOC,cAAc,CAAC1B,YAAY,wBAAwB;QACxD2B,OAAOnB;QACPoB,YAAY;QACZC,cAAc;IAChB;IAEA,gEAAgE;IAChE,KAAKhC;AACP;AAEAwB"}