{"version": 3, "sources": ["../../../../src/server/web/spec-extension/response.ts"], "names": ["NextResponse", "INTERNALS", "Symbol", "REDIRECTS", "Set", "handleMiddlewareField", "init", "headers", "request", "Headers", "Error", "keys", "key", "value", "set", "push", "join", "Response", "constructor", "body", "cookies", "ResponseCookies", "cookiesProxy", "Proxy", "get", "target", "prop", "receiver", "args", "result", "Reflect", "apply", "newHeaders", "getAll", "map", "cookie", "string<PERSON><PERSON><PERSON><PERSON>", "ReflectAdapter", "url", "NextURL", "toNodeOutgoingHttpHeaders", "nextConfig", "undefined", "for", "bodyUsed", "Object", "fromEntries", "ok", "redirected", "status", "statusText", "type", "json", "response", "redirect", "has", "RangeError", "initObj", "validateURL", "rewrite", "destination", "next"], "mappings": ";;;;+BAmCaA;;;eAAAA;;;yBAnCmB;yBAER;uBAC+B;yBACxB;0BAEC;AAEhC,MAAMC,YAAYC,OAAO;AACzB,MAAMC,YAAY,IAAIC,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAEnD,SAASC,sBACPC,IAAwC,EACxCC,OAAgB;QAEZD;IAAJ,IAAIA,yBAAAA,gBAAAA,KAAME,OAAO,qBAAbF,cAAeC,OAAO,EAAE;QAC1B,IAAI,CAAED,CAAAA,KAAKE,OAAO,CAACD,OAAO,YAAYE,OAAM,GAAI;YAC9C,MAAM,IAAIC,MAAM;QAClB;QAEA,MAAMC,OAAO,EAAE;QACf,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIP,KAAKE,OAAO,CAACD,OAAO,CAAE;YAC/CA,QAAQO,GAAG,CAAC,0BAA0BF,KAAKC;YAC3CF,KAAKI,IAAI,CAACH;QACZ;QAEAL,QAAQO,GAAG,CAAC,iCAAiCH,KAAKK,IAAI,CAAC;IACzD;AACF;AAOO,MAAMhB,qBAAqCiB;IAOhDC,YAAYC,IAAsB,EAAEb,OAAqB,CAAC,CAAC,CAAE;QAC3D,KAAK,CAACa,MAAMb;QAEZ,MAAMC,UAAU,IAAI,CAACA,OAAO;QAC5B,MAAMa,UAAU,IAAIC,yBAAe,CAACd;QAEpC,MAAMe,eAAe,IAAIC,MAAMH,SAAS;YACtCI,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;wBAAO;4BACV,OAAO,CAAC,GAAGE;gCACT,MAAMC,SAASC,QAAQC,KAAK,CAACN,MAAM,CAACC,KAAK,EAAED,QAAQG;gCACnD,MAAMI,aAAa,IAAIvB,QAAQF;gCAE/B,IAAIsB,kBAAkBR,yBAAe,EAAE;oCACrCd,QAAQO,GAAG,CACT,2BACAe,OACGI,MAAM,GACNC,GAAG,CAAC,CAACC,SAAWC,IAAAA,wBAAe,EAACD,SAChCnB,IAAI,CAAC;gCAEZ;gCAEAX,sBAAsBC,MAAM0B;gCAC5B,OAAOH;4BACT;wBACF;oBACA;wBACE,OAAOQ,uBAAc,CAACb,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;QAEA,IAAI,CAAC1B,UAAU,GAAG;YAChBmB,SAASE;YACTgB,KAAKhC,KAAKgC,GAAG,GACT,IAAIC,gBAAO,CAACjC,KAAKgC,GAAG,EAAE;gBACpB/B,SAASiC,IAAAA,gCAAyB,EAACjC;gBACnCkC,YAAYnC,KAAKmC,UAAU;YAC7B,KACAC;QACN;IACF;IAEA,CAACxC,OAAOyC,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLvB,SAAS,IAAI,CAACA,OAAO;YACrBkB,KAAK,IAAI,CAACA,GAAG;YACb,mCAAmC;YACnCnB,MAAM,IAAI,CAACA,IAAI;YACfyB,UAAU,IAAI,CAACA,QAAQ;YACvBrC,SAASsC,OAAOC,WAAW,CAAC,IAAI,CAACvC,OAAO;YACxCwC,IAAI,IAAI,CAACA,EAAE;YACXC,YAAY,IAAI,CAACA,UAAU;YAC3BC,QAAQ,IAAI,CAACA,MAAM;YACnBC,YAAY,IAAI,CAACA,UAAU;YAC3BC,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEA,IAAW/B,UAAU;QACnB,OAAO,IAAI,CAACnB,UAAU,CAACmB,OAAO;IAChC;IAEA,OAAOgC,KACLjC,IAAc,EACdb,IAAmB,EACK;QACxB,MAAM+C,WAAqBpC,SAASmC,IAAI,CAACjC,MAAMb;QAC/C,OAAO,IAAIN,aAAaqD,SAASlC,IAAI,EAAEkC;IACzC;IAEA,OAAOC,SAAShB,GAA2B,EAAEhC,IAA4B,EAAE;QACzE,MAAM2C,SAAS,OAAO3C,SAAS,WAAWA,OAAOA,CAAAA,wBAAAA,KAAM2C,MAAM,KAAI;QACjE,IAAI,CAAC9C,UAAUoD,GAAG,CAACN,SAAS;YAC1B,MAAM,IAAIO,WACR;QAEJ;QACA,MAAMC,UAAU,OAAOnD,SAAS,WAAWA,OAAO,CAAC;QACnD,MAAMC,UAAU,IAAIE,QAAQgD,2BAAAA,QAASlD,OAAO;QAC5CA,QAAQO,GAAG,CAAC,YAAY4C,IAAAA,kBAAW,EAACpB;QAEpC,OAAO,IAAItC,aAAa,MAAM;YAC5B,GAAGyD,OAAO;YACVlD;YACA0C;QACF;IACF;IAEA,OAAOU,QACLC,WAAmC,EACnCtD,IAA6B,EAC7B;QACA,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,wBAAwB4C,IAAAA,kBAAW,EAACE;QAEhDvD,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIP,aAAa,MAAM;YAAE,GAAGM,IAAI;YAAEC;QAAQ;IACnD;IAEA,OAAOsD,KAAKvD,IAA6B,EAAE;QACzC,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,qBAAqB;QAEjCT,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIP,aAAa,MAAM;YAAE,GAAGM,IAAI;YAAEC;QAAQ;IACnD;AACF"}