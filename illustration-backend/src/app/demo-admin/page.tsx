'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { DollarSign, Clock, CheckCircle, Users, ShoppingCart, Calendar, BarChart3, TrendingUp, Activity, Search, Menu, X, ArrowUpDown, ArrowUp, ArrowDown, Trash2 } from 'lucide-react'

// 演示数据
const initialOrders = [
  {
    id: 'demo-1',
    sequence_number: 1, // 订单序号
    order_id: 'ORD-20241201-ABC123',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    order_status: 'in_progress',
    payment_status: 'completed',
    created_at: '2024-12-01T10:30:00Z',
    status_updated_at: '2024-12-01T14:30:00Z',
    requirements: '需要一个现代简约风格的品牌插画，主要用于网站首页展示。希望能体现科技感和专业性。',
    paypal_transaction_id: 'PAYPAL-TXN-123456',
    reference_images: [
      'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400',
      'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400'
    ],
    completed_images: [
      'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600'
    ]
  },
  {
    id: 'demo-2',
    order_id: 'ORD-20241201-DEF456',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    order_status: 'pending',
    payment_status: 'completed',
    created_at: '2024-12-01T09:15:00Z',
    status_updated_at: '2024-12-01T09:15:00Z',
    requirements: '设计一个可爱的卡通角色，用于儿童教育应用。希望角色活泼可爱，有亲和力。',
    paypal_transaction_id: 'PAYPAL-TXN-456789',
    reference_images: [
      'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=400',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400'
    ],
    completed_images: []
  },
  {
    id: 'demo-3',
    order_id: 'ORD-20241130-GHI789',
    service_type: 'scene',
    service_name: '场景插画',
    price: 10,
    customer_contact: '<EMAIL>',
    order_status: 'completed',
    payment_status: 'completed',
    created_at: '2024-11-30T16:45:00Z',
    status_updated_at: '2024-12-01T12:00:00Z',
    requirements: '创作一幅梦幻森林场景插画，用于小说封面。需要神秘而美丽的氛围。',
    paypal_transaction_id: 'PAYPAL-TXN-789012',
    reference_images: [
      'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400',
      'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400'
    ],
    completed_images: [
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600',
      'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?w=600'
    ]
  },
  {
    id: 'demo-4',
    order_id: 'ORD-20241201-JKL012',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    order_status: 'completed',
    payment_status: 'completed',
    created_at: '2024-11-29T08:20:00Z',
    status_updated_at: '2024-11-30T10:15:00Z',
    requirements: '为新创公司设计logo插画，需要体现创新和活力。',
    paypal_transaction_id: 'PAYPAL-TXN-012345',
    reference_images: [
      'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400',
      'https://images.unsplash.com/photo-1551434678-e076c223a692?w=400'
    ],
    completed_images: [
      'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=600'
    ]
  },
  {
    id: 'demo-5',
    order_id: 'ORD-20241201-MNO345',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    order_status: 'in_progress',
    payment_status: 'completed',
    created_at: '2024-12-01T11:45:00Z',
    status_updated_at: '2024-12-01T15:20:00Z',
    requirements: '游戏主角设计，需要勇敢坚毅的形象。',
    paypal_transaction_id: 'PAYPAL-TXN-345678',
    reference_images: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=400'
    ],
    completed_images: []
  },
  {
    id: 'demo-6',
    order_id: 'ORD-20241201-PQR678',
    service_type: 'scene',
    service_name: '场景插画',
    price: 10,
    customer_contact: '<EMAIL>',
    order_status: 'pending',
    payment_status: 'completed',
    created_at: '2024-12-01T13:30:00Z',
    status_updated_at: '2024-12-01T13:30:00Z',
    requirements: '科幻小说封面，需要未来城市场景。',
    paypal_transaction_id: 'PAYPAL-TXN-678901'
  },
  {
    id: 'demo-7',
    order_id: 'ORD-20241130-STU901',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    order_status: 'completed',
    payment_status: 'completed',
    created_at: '2024-11-30T14:15:00Z',
    status_updated_at: '2024-12-01T09:30:00Z',
    requirements: '餐厅菜单插画，温馨家庭风格。',
    paypal_transaction_id: 'PAYPAL-TXN-901234'
  },
  {
    id: 'demo-8',
    order_id: 'ORD-20241201-VWX234',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    order_status: 'pending',
    payment_status: 'completed',
    created_at: '2024-12-01T16:00:00Z',
    status_updated_at: '2024-12-01T16:00:00Z',
    requirements: '动画片配角设计，需要幽默搞笑的形象。',
    paypal_transaction_id: 'PAYPAL-TXN-234567'
  },
  {
    id: 'demo-9',
    order_id: 'ORD-20241201-YZA567',
    service_type: 'scene',
    service_name: '场景插画',
    price: 10,
    customer_contact: '<EMAIL>',
    order_status: 'in_progress',
    payment_status: 'completed',
    created_at: '2024-12-01T12:20:00Z',
    status_updated_at: '2024-12-01T16:45:00Z',
    requirements: '杂志内页插画，描绘春天的田园风光。',
    paypal_transaction_id: 'PAYPAL-TXN-567890'
  },
  {
    id: 'demo-10',
    order_id: 'ORD-20241130-BCD890',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    order_status: 'completed',
    payment_status: 'completed',
    created_at: '2024-11-30T09:30:00Z',
    status_updated_at: '2024-11-30T18:45:00Z',
    requirements: '时尚品牌宣传插画，需要优雅现代的风格。',
    paypal_transaction_id: 'PAYPAL-TXN-890123'
  },
  {
    id: 'demo-11',
    order_id: 'ORD-20241201-EFG123',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    order_status: 'pending',
    payment_status: 'completed',
    created_at: '2024-12-01T14:45:00Z',
    status_updated_at: '2024-12-01T14:45:00Z',
    requirements: '教育软件吉祥物设计，需要亲切友好的形象。',
    paypal_transaction_id: 'PAYPAL-TXN-123890'
  },
  {
    id: 'demo-12',
    order_id: 'ORD-20241201-HIJ456',
    service_type: 'scene',
    service_name: '场景插画',
    price: 10,
    customer_contact: '<EMAIL>',
    order_status: 'completed',
    payment_status: 'completed',
    created_at: '2024-11-29T11:20:00Z',
    status_updated_at: '2024-11-30T14:30:00Z',
    requirements: '旅游宣传册插画，展现海滨度假胜地。',
    paypal_transaction_id: 'PAYPAL-TXN-456123'
  },
  {
    id: 'demo-13',
    order_id: 'ORD-20241201-KLM789',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    order_status: 'in_progress',
    payment_status: 'completed',
    created_at: '2024-12-01T08:15:00Z',
    status_updated_at: '2024-12-01T12:30:00Z',
    requirements: '咖啡店品牌插画，温暖舒适的氛围。',
    paypal_transaction_id: 'PAYPAL-TXN-789456'
  },
  {
    id: 'demo-14',
    order_id: 'ORD-20241201-NOP012',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    order_status: 'pending',
    payment_status: 'completed',
    created_at: '2024-12-01T17:30:00Z',
    status_updated_at: '2024-12-01T17:30:00Z',
    requirements: '手机应用角色设计，需要现代科技感。',
    paypal_transaction_id: 'PAYPAL-TXN-012789'
  },
  {
    id: 'demo-15',
    order_id: 'ORD-20241130-QRS345',
    service_type: 'scene',
    service_name: '场景插画',
    price: 10,
    customer_contact: '<EMAIL>',
    order_status: 'completed',
    payment_status: 'completed',
    created_at: '2024-11-30T13:45:00Z',
    status_updated_at: '2024-12-01T08:20:00Z',
    requirements: '儿童读物插画，描绘魔法森林的奇幻世界。',
    paypal_transaction_id: 'PAYPAL-TXN-345012'
  },
  {
    id: 'demo-16',
    order_id: 'ORD-20241201-TUV678',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    order_status: 'in_progress',
    payment_status: 'completed',
    created_at: '2024-12-01T15:20:00Z',
    status_updated_at: '2024-12-01T18:15:00Z',
    requirements: '健康养生品牌插画，自然清新的风格。',
    paypal_transaction_id: 'PAYPAL-TXN-678345'
  },
  {
    id: 'demo-17',
    order_id: 'ORD-20241201-WXY901',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    order_status: 'completed',
    payment_status: 'completed',
    created_at: '2024-11-29T16:30:00Z',
    status_updated_at: '2024-11-30T11:45:00Z',
    requirements: '体育品牌吉祥物，充满活力和运动感。',
    paypal_transaction_id: 'PAYPAL-TXN-901678'
  },
  {
    id: 'demo-18',
    order_id: 'ORD-20241201-ZAB234',
    service_type: 'scene',
    service_name: '场景插画',
    price: 10,
    customer_contact: '<EMAIL>',
    order_status: 'pending',
    payment_status: 'completed',
    created_at: '2024-12-01T19:15:00Z',
    status_updated_at: '2024-12-01T19:15:00Z',
    requirements: '博物馆展览插画，展现古代文明的辉煌。',
    paypal_transaction_id: 'PAYPAL-TXN-234901'
  },
  {
    id: 'demo-19',
    order_id: 'ORD-20241201-CDE567',
    service_type: 'brand',
    service_name: '品牌插画',
    price: 5,
    customer_contact: '<EMAIL>',
    order_status: 'pending',
    payment_status: 'completed',
    created_at: '2024-12-01T20:00:00Z',
    status_updated_at: '2024-12-01T20:00:00Z',
    requirements: '科技公司品牌插画，未来感和创新精神。',
    paypal_transaction_id: 'PAYPAL-TXN-567234'
  },
  {
    id: 'demo-20',
    order_id: 'ORD-20241201-FGH890',
    service_type: 'character',
    service_name: '角色设计',
    price: 7,
    customer_contact: '<EMAIL>',
    order_status: 'in_progress',
    payment_status: 'completed',
    created_at: '2024-12-01T07:45:00Z',
    status_updated_at: '2024-12-01T11:20:00Z',
    requirements: '慈善组织吉祥物，温暖关爱的形象。',
    paypal_transaction_id: 'PAYPAL-TXN-890567'
  }
]

export default function DemoAdminDashboard() {
  const [selectedOrder, setSelectedOrder] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('orders')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResult, setSearchResult] = useState<any>(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'in_progress' | 'completed'>('all')
  const [priceFilter, setPriceFilter] = useState<'all' | 5 | 7 | 10>('all')
  const [sortBy, setSortBy] = useState<'created_at' | 'price'>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentTime, setCurrentTime] = useState(new Date())
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showBatchActions, setShowBatchActions] = useState(false)
  const [deletedOrders, setDeletedOrders] = useState<any[]>([]) // 回收站订单
  const [scrollPosition, setScrollPosition] = useState(0) // 记录滚动位置
  const [nextSequenceNumber, setNextSequenceNumber] = useState(21) // 下一个序号（当前有20个订单）
  const [showAddOrderForm, setShowAddOrderForm] = useState(false) // 显示添加订单表单
  const [editingNotes, setEditingNotes] = useState(false) // 是否正在编辑备注
  const [tempNotes, setTempNotes] = useState('') // 临时备注内容
  const [currentPage, setCurrentPage] = useState(1) // 当前页码
  const [pageSize] = useState(20) // 每页显示数量
  const [jumpToPage, setJumpToPage] = useState('') // 跳转页码输入
  const [batchPanelPosition, setBatchPanelPosition] = useState({ x: 20, y: 100 }) // 批量操作面板位置
  const [isDragging, setIsDragging] = useState(false) // 是否正在拖拽
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 }) // 拖拽偏移量

  const [timeAdjustmentInput, setTimeAdjustmentInput] = useState('') // 时间调整输入框
  const [timeAdjustments, setTimeAdjustments] = useState<{[key: string]: number}>({}) // 时间调整记录

  const [newOrderForm, setNewOrderForm] = useState({
    service_name: '',
    service_type: 'brand',
    price: 5,
    customer_contact: '',
    requirements: '',
    customer_photos: [] as string[],
    style_references: [] as string[],
    admin_notes: '' // 管理员备注
  })
  const [orders, setOrders] = useState(() =>
    // 为初始订单添加序号
    initialOrders.map((order, index) => ({
      ...order,
      sequence_number: index + 1
    }))
  )

  // 每分钟更新一次时间，保持倒计时准确
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000) // 每分钟更新

    return () => clearInterval(timer)
  }, [])

  // 为所有订单添加默认图片和备注
  useEffect(() => {
    setOrders(prevOrders =>
      prevOrders.map(order => ({
        ...order,
        reference_images: order.reference_images || [
          'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400',
          'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400'
        ],
        customer_photos: order.customer_photos || [
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400'
        ],
        style_references: order.style_references || [
          'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400'
        ],
        completed_images: order.order_status === 'completed' ? (order.completed_images || [
          'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600'
        ]) : [],
        admin_notes: order.admin_notes || (
          // 为部分订单添加示例备注
          order.id === 'demo-1' ? '客户要求加急处理，需要在2天内完成' :
          order.id === 'demo-3' ? '客户对颜色要求很高，需要多次确认' :
          order.id === 'demo-7' ? '重要客户，优先处理' :
          order.id === 'demo-12' ? '客户提供的参考图片质量较低，需要沟通' :
          ''
        )
      }))
    )
  }, [])

  const menuItems = [
    { id: 'orders', label: '订单', icon: ShoppingCart },
    { id: 'statistics', label: '数据统计', icon: BarChart3 },
    { id: 'search', label: '查询', icon: Search },
    { id: 'trash', label: '回收站', icon: Trash2 }
  ]

  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResult(null)
      return
    }

    // 根据订单ID或PayPal交易ID查询
    const result = orders.find(order =>
      order.order_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (order.paypal_transaction_id && order.paypal_transaction_id.toLowerCase().includes(searchQuery.toLowerCase()))
    )

    setSearchResult(result || 'not_found')
  }

  const handleSort = (field: 'created_at' | 'price') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  const getFilteredAndSortedOrders = () => {
    // 先筛选
    let filtered = orders
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.order_status === statusFilter)
    }
    if (priceFilter !== 'all') {
      filtered = filtered.filter(order => order.price === priceFilter)
    }

    // 再排序
    return [...filtered].sort((a, b) => {
      let aValue: any = a[sortBy]
      let bValue: any = b[sortBy]

      if (sortBy === 'created_at') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      } else if (sortBy === 'price') {
        aValue = Number(aValue)
        bValue = Number(bValue)
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }

  // 获取当前页的订单
  const getCurrentPageOrders = () => {
    const allOrders = getFilteredAndSortedOrders()
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    return allOrders.slice(startIndex, endIndex)
  }

  // 获取总页数
  const getTotalPages = () => {
    return Math.ceil(getFilteredAndSortedOrders().length / pageSize)
  }

  // 跳转到指定页
  const handleJumpToPage = () => {
    const page = parseInt(jumpToPage)
    if (page >= 1 && page <= getTotalPages()) {
      setCurrentPage(page)
      setJumpToPage('')
    }
  }

  // 改变筛选条件并重置到第一页
  const handleStatusFilterChange = (status: 'all' | 'pending' | 'in_progress' | 'completed') => {
    setStatusFilter(status)
    setCurrentPage(1)
  }

  const handlePriceFilterChange = (price: 'all' | 5 | 7 | 10) => {
    setPriceFilter(price)
    setCurrentPage(1)
  }

  // 拖拽功能
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragOffset({
      x: e.clientX - batchPanelPosition.x,
      y: e.clientY - batchPanelPosition.y
    })
  }



  // 添加全局鼠标事件监听
  React.useEffect(() => {
    const handleMouseMoveGlobal = (e: MouseEvent) => {
      if (isDragging) {
        setBatchPanelPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y
        })
      }
    }

    const handleMouseUpGlobal = () => {
      setIsDragging(false)
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMoveGlobal)
      document.addEventListener('mouseup', handleMouseUpGlobal)
      return () => {
        document.removeEventListener('mousemove', handleMouseMoveGlobal)
        document.removeEventListener('mouseup', handleMouseUpGlobal)
      }
    }
  }, [isDragging, dragOffset])



  const getSortIcon = (field: 'created_at' | 'price') => {
    if (sortBy !== field) {
      return <ArrowUpDown className="h-4 w-4" />
    }
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
  }

  const getStatusCount = (status: 'all' | 'pending' | 'in_progress' | 'completed') => {
    if (status === 'all') return orders.length
    return orders.filter(order => order.order_status === status).length
  }

  const getPriceCount = (price: 'all' | 5 | 7 | 10) => {
    if (price === 'all') return orders.length
    return orders.filter(order => order.price === price).length
  }

  // 根据价格确定处理小时数
  const getProcessingHours = (price: number) => {
    if (price === 5) return 48   // 48小时
    if (price === 7) return 72   // 72小时
    if (price === 10) return 96  // 96小时
    return 48 // 默认
  }

  // 制作时间固定2小时
  const PRODUCTION_HOURS = 2

  // 计算剩余时间
  const getRemainingTime = (order: any) => {
    const now = currentTime
    const createdAt = new Date(order.created_at)
    const statusUpdatedAt = new Date(order.status_updated_at)

    // 获取该订单的时间调整（以小时为单位）
    const timeAdjustmentHours = timeAdjustments[order.id] || 0

    if (order.order_status === 'pending') {
      // 待处理：从创建时间开始计算
      const processingHours = getProcessingHours(order.price)
      const deadlineTime = new Date(createdAt.getTime() + processingHours * 60 * 60 * 1000)
      const baseRemainingMs = deadlineTime.getTime() - now.getTime()
      // 直接在剩余时间上加上调整量
      const adjustedRemainingMs = baseRemainingMs + (timeAdjustmentHours * 60 * 60 * 1000)
      return { remainingMs: adjustedRemainingMs, totalHours: processingHours, type: 'processing' }
    } else if (order.order_status === 'in_progress') {
      // 制作中：从状态更新时间开始计算2小时
      const deadlineTime = new Date(statusUpdatedAt.getTime() + PRODUCTION_HOURS * 60 * 60 * 1000)
      const baseRemainingMs = deadlineTime.getTime() - now.getTime()
      // 直接在剩余时间上加上调整量
      const adjustedRemainingMs = baseRemainingMs + (timeAdjustmentHours * 60 * 60 * 1000)
      return { remainingMs: adjustedRemainingMs, totalHours: PRODUCTION_HOURS, type: 'production' }
    } else {
      // 已完成：计算实际用时
      const totalMs = statusUpdatedAt.getTime() - createdAt.getTime()
      return { remainingMs: 0, totalHours: totalMs / (1000 * 60 * 60), type: 'completed' }
    }
  }

  // 格式化时间显示
  const formatTimeRemaining = (remainingMs: number) => {
    if (remainingMs <= 0) return '已超时'

    const hours = Math.floor(remainingMs / (1000 * 60 * 60))
    const minutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }

  // 获取时间状态颜色
  const getTimeStatus = (order: any) => {
    const { remainingMs, totalHours } = getRemainingTime(order)

    if (order.order_status === 'completed') {
      return 'completed'
    }

    if (remainingMs <= 0) {
      return 'overdue' // 已超时
    }

    const totalMs = totalHours * 60 * 60 * 1000
    const remainingPercent = remainingMs / totalMs

    if (remainingPercent < 0.25) {
      return 'warning' // 即将到期
    }

    return 'normal' // 正常
  }

  // 批量选择功能
  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => {
      if (prev.includes(orderId)) {
        return prev.filter(id => id !== orderId)
      } else {
        return [...prev, orderId]
      }
    })
  }

  // 批量调整时间 - 记录调整量
  const handleBatchTimeAdjustment = () => {
    const hours = parseFloat(timeAdjustmentInput)
    if (isNaN(hours) || hours === 0) {
      alert('请输入有效的时间数值（可以是负数）')
      return
    }

    // 记录每个选中订单的时间调整
    setTimeAdjustments(prev => {
      const newAdjustments = { ...prev }
      selectedOrders.forEach(orderId => {
        const currentAdjustment = newAdjustments[orderId] || 0
        newAdjustments[orderId] = currentAdjustment + hours
      })
      return newAdjustments
    })

    // 清空输入框
    setTimeAdjustmentInput('')
  }

  const handleSelectAll = () => {
    const filteredOrders = getFilteredAndSortedOrders()
    if (selectedOrders.length === filteredOrders.length) {
      setSelectedOrders([])
    } else {
      setSelectedOrders(filteredOrders.map(order => order.id))
    }
  }

  const handleBatchStatusChange = (newStatus: 'pending' | 'in_progress' | 'completed') => {
    const now = new Date().toISOString()

    setOrders(prevOrders =>
      prevOrders.map(order => {
        if (selectedOrders.includes(order.id)) {
          return {
            ...order,
            order_status: newStatus,
            status_updated_at: now
          }
        }
        return order
      })
    )

    setSelectedOrders([])
    setShowBatchActions(false)
  }



  // 开始处理订单
  const handleStartProcessing = (orderId: string) => {
    console.log('开始处理订单:', orderId)
    const now = new Date().toISOString()
    setOrders(prevOrders =>
      prevOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            order_status: 'in_progress' as const,
            status_updated_at: now
          }
        }
        return order
      })
    )
    // 更新选中的订单状态
    setSelectedOrder(prev => prev ? {
      ...prev,
      order_status: 'in_progress' as const,
      status_updated_at: now
    } : null)
  }

  // 上传完成作品
  const handleUploadComplete = (orderId: string, images: string[]) => {
    console.log('上传完成作品:', orderId, images)
    const now = new Date().toISOString()
    setOrders(prevOrders =>
      prevOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            order_status: 'completed' as const,
            status_updated_at: now,
            completed_images: images
          }
        }
        return order
      })
    )
    // 更新选中的订单状态
    setSelectedOrder(prev => prev ? {
      ...prev,
      order_status: 'completed' as const,
      completed_images: images,
      status_updated_at: now
    } : null)
  }

  // 删除订单到回收站
  const handleDeleteOrder = (orderId: string) => {
    const orderToDelete = orders.find(order => order.id === orderId)
    if (orderToDelete) {
      // 添加删除时间戳
      const deletedOrder = {
        ...orderToDelete,
        deleted_at: new Date().toISOString()
      }
      setDeletedOrders(prev => [...prev, deletedOrder])
      setOrders(prev => prev.filter(order => order.id !== orderId))
      // 如果当前选中的是被删除的订单，清除选中状态
      if (selectedOrder?.id === orderId) {
        setSelectedOrder(null)
      }
    }
  }

  // 从回收站恢复订单
  const handleRestoreOrder = (orderId: string) => {
    const orderToRestore = deletedOrders.find(order => order.id === orderId)
    if (orderToRestore) {
      // 移除删除时间戳，并重置状态为待处理，保持原有序号
      const { deleted_at, ...restoredOrder } = orderToRestore
      const resetOrder = {
        ...restoredOrder,
        order_status: 'pending' as const,
        status_updated_at: new Date().toISOString()
        // sequence_number 保持不变
      }
      setOrders(prev => [...prev, resetOrder])
      setDeletedOrders(prev => prev.filter(order => order.id !== orderId))
    }
  }

  // 永久删除订单
  const handlePermanentDelete = (orderId: string) => {
    setDeletedOrders(prev => prev.filter(order => order.id !== orderId))
  }

  // 批量删除订单
  const handleBatchDelete = () => {
    const ordersToDelete = orders.filter(order => selectedOrders.includes(order.id))
    const deletedOrdersWithTimestamp = ordersToDelete.map(order => ({
      ...order,
      deleted_at: new Date().toISOString()
    }))

    setDeletedOrders(prev => [...prev, ...deletedOrdersWithTimestamp])
    setOrders(prev => prev.filter(order => !selectedOrders.includes(order.id)))
    setSelectedOrders([])
    setShowBatchActions(false)
  }

  // 添加新订单
  const handleAddNewOrder = () => {
    if (!newOrderForm.service_name || !newOrderForm.customer_contact || !newOrderForm.requirements) {
      alert('请填写完整的订单信息')
      return
    }

    const newOrder = {
      id: `manual-${nextSequenceNumber}`,
      sequence_number: nextSequenceNumber,
      order_id: `ORD-${new Date().toISOString().slice(0,10).replace(/-/g,'')}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      service_type: newOrderForm.service_type,
      service_name: newOrderForm.service_name,
      price: newOrderForm.price,
      customer_contact: newOrderForm.customer_contact,
      order_status: 'pending' as const,
      payment_status: 'completed',
      created_at: new Date().toISOString(),
      status_updated_at: new Date().toISOString(),
      requirements: newOrderForm.requirements,
      paypal_transaction_id: `MANUAL-${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
      customer_photos: newOrderForm.customer_photos,
      style_references: newOrderForm.style_references,
      completed_images: [],
      admin_notes: newOrderForm.admin_notes
    }

    setOrders(prev => [newOrder, ...prev])
    setNextSequenceNumber(prev => prev + 1)

    // 重置表单
    setNewOrderForm({
      service_name: '',
      service_type: 'brand',
      price: 5,
      customer_contact: '',
      requirements: '',
      customer_photos: [],
      style_references: [],
      admin_notes: ''
    })
    setShowAddOrderForm(false)
  }

  // 模拟图片上传
  const handleImageUpload = (type: 'customer_photos' | 'style_references') => {
    // 模拟上传，实际应该是文件上传逻辑
    const mockImages = [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
      'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400'
    ]
    const randomImage = mockImages[Math.floor(Math.random() * mockImages.length)]

    setNewOrderForm(prev => ({
      ...prev,
      [type]: [...prev[type], randomImage]
    }))
  }

  // 删除图片
  const handleRemoveImage = (type: 'customer_photos' | 'style_references', index: number) => {
    setNewOrderForm(prev => ({
      ...prev,
      [type]: prev[type].filter((_, i) => i !== index)
    }))
  }

  // 开始编辑备注
  const handleStartEditNotes = () => {
    setTempNotes(selectedOrder?.admin_notes || '')
    setEditingNotes(true)
  }

  // 保存备注
  const handleSaveNotes = () => {
    if (selectedOrder) {
      setOrders(prev => prev.map(order =>
        order.id === selectedOrder.id
          ? { ...order, admin_notes: tempNotes }
          : order
      ))
      setSelectedOrder(prev => prev ? { ...prev, admin_notes: tempNotes } : null)
    }
    setEditingNotes(false)
    setTempNotes('')
  }

  // 取消编辑备注
  const handleCancelEditNotes = () => {
    setEditingNotes(false)
    setTempNotes('')
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'orders':
        return (
          <div>

            {/* 添加订单表单弹窗 */}
            {showAddOrderForm && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                  <div className="p-6 border-b">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">手动添加订单</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAddOrderForm(false)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="p-6 space-y-6">
                    {/* 基本信息 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          服务名称 *
                        </label>
                        <Input
                          value={newOrderForm.service_name}
                          onChange={(e) => setNewOrderForm(prev => ({...prev, service_name: e.target.value}))}
                          placeholder="例如：品牌插画设计"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          服务类型
                        </label>
                        <select
                          value={newOrderForm.service_type}
                          onChange={(e) => setNewOrderForm(prev => ({...prev, service_type: e.target.value as any}))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="brand">品牌插画</option>
                          <option value="character">角色设计</option>
                          <option value="illustration">插画设计</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          客户联系方式 *
                        </label>
                        <Input
                          value={newOrderForm.customer_contact}
                          onChange={(e) => setNewOrderForm(prev => ({...prev, customer_contact: e.target.value}))}
                          placeholder="邮箱或其他联系方式"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          价格 (USD)
                        </label>
                        <Input
                          type="number"
                          min="1"
                          max="100"
                          value={newOrderForm.price}
                          onChange={(e) => setNewOrderForm(prev => ({...prev, price: parseInt(e.target.value) || 5}))}
                        />
                      </div>
                    </div>

                    {/* 需求描述 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        需求描述 *
                      </label>
                      <textarea
                        value={newOrderForm.requirements}
                        onChange={(e) => setNewOrderForm(prev => ({...prev, requirements: e.target.value}))}
                        placeholder="详细描述客户的需求..."
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    {/* 管理员备注 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        管理员备注
                      </label>
                      <textarea
                        value={newOrderForm.admin_notes}
                        onChange={(e) => setNewOrderForm(prev => ({...prev, admin_notes: e.target.value}))}
                        placeholder="内部备注信息，客户不可见..."
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">此备注仅管理员可见，用于内部沟通和记录</p>
                    </div>

                    {/* 客户照片 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        客户照片
                      </label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                        <div className="flex items-center justify-center mb-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => handleImageUpload('customer_photos')}
                          >
                            上传客户照片 (模拟)
                          </Button>
                        </div>
                        {newOrderForm.customer_photos.length > 0 && (
                          <div className="grid grid-cols-3 gap-2">
                            {newOrderForm.customer_photos.map((img, index) => (
                              <div key={index} className="relative">
                                <img src={img} alt={`客户照片 ${index + 1}`} className="w-full h-20 object-cover rounded" />
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  className="absolute top-1 right-1 h-6 w-6 p-0"
                                  onClick={() => handleRemoveImage('customer_photos', index)}
                                >
                                  ×
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 风格参考图 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        风格参考图
                      </label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                        <div className="flex items-center justify-center mb-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => handleImageUpload('style_references')}
                          >
                            上传风格参考 (模拟)
                          </Button>
                        </div>
                        {newOrderForm.style_references.length > 0 && (
                          <div className="grid grid-cols-3 gap-2">
                            {newOrderForm.style_references.map((img, index) => (
                              <div key={index} className="relative">
                                <img src={img} alt={`风格参考 ${index + 1}`} className="w-full h-20 object-cover rounded" />
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  className="absolute top-1 right-1 h-6 w-6 p-0"
                                  onClick={() => handleRemoveImage('style_references', index)}
                                >
                                  ×
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="p-6 border-t bg-gray-50 flex justify-end space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowAddOrderForm(false)}
                    >
                      取消
                    </Button>
                    <Button
                      onClick={handleAddNewOrder}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      添加订单
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* 筛选和排序 */}
            <div className="mb-6 bg-white rounded-lg shadow-sm border p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-700">筛选栏</h3>
                <Button
                  onClick={() => setShowAddOrderForm(true)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  + 手动添加订单
                </Button>
              </div>
              <div className="flex flex-col gap-4">
                {/* 状态筛选 */}
                <div>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant={statusFilter === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleStatusFilterChange('all')}
                    >
                      全部 ({getStatusCount('all')})
                    </Button>
                    <Button
                      variant={statusFilter === 'pending' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleStatusFilterChange('pending')}
                    >
                      待处理 ({getStatusCount('pending')})
                    </Button>
                    <Button
                      variant={statusFilter === 'in_progress' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleStatusFilterChange('in_progress')}
                    >
                      制作中 ({getStatusCount('in_progress')})
                    </Button>
                    <Button
                      variant={statusFilter === 'completed' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleStatusFilterChange('completed')}
                    >
                      已完成 ({getStatusCount('completed')})
                    </Button>
                    <Button
                      variant={priceFilter === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePriceFilterChange('all')}
                    >
                      全部金额 ({getPriceCount('all')})
                    </Button>
                    <Button
                      variant={priceFilter === 5 ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePriceFilterChange(5)}
                    >
                      $5 ({getPriceCount(5)})
                    </Button>
                    <Button
                      variant={priceFilter === 7 ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePriceFilterChange(7)}
                    >
                      $7 ({getPriceCount(7)})
                    </Button>
                    <Button
                      variant={priceFilter === 10 ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePriceFilterChange(10)}
                    >
                      $10 ({getPriceCount(10)})
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* 批量操作栏 - 可拖拽悬浮窗口 */}
            {selectedOrders.length > 0 && (
              <div
                className="fixed z-50 bg-white border-2 border-blue-300 rounded-xl shadow-2xl min-w-80 max-w-96"
                style={{
                  left: `${batchPanelPosition.x}px`,
                  top: `${batchPanelPosition.y}px`,
                  cursor: isDragging ? 'grabbing' : 'grab'
                }}
              >
                {/* 拖拽标题栏 */}
                <div
                  className="bg-blue-500 text-white px-4 py-2 rounded-t-xl cursor-grab active:cursor-grabbing flex items-center justify-between"
                  onMouseDown={handleMouseDown}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">
                      📋 已选择 {selectedOrders.length} 个订单 - 修改为
                    </span>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setSelectedOrders([])}
                    className="text-white hover:bg-blue-600 h-6 w-6 p-0"
                  >
                    ✕
                  </Button>
                </div>

                {/* 操作按钮区域 */}
                <div className="p-3 space-y-3">
                  {/* 状态修改按钮 */}
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleBatchStatusChange('pending')}
                      className="bg-gray-500 hover:bg-gray-600 text-white w-full"
                    >
                      ⏳ 待处理
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleBatchStatusChange('in_progress')}
                      className="bg-blue-500 hover:bg-blue-600 text-white w-full"
                    >
                      🔄 制作中
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleBatchStatusChange('completed')}
                      className="bg-green-500 hover:bg-green-600 text-white w-full"
                    >
                      ✅ 已完成
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={handleBatchDelete}
                      className="w-full"
                    >
                      🗑️ 删除
                    </Button>
                  </div>

                  {/* 时间调整功能 */}
                  <div className="border-t pt-3">
                    <div className="text-xs text-gray-600 mb-3 font-medium">⏰ 剩余时间调整</div>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <input
                          type="number"
                          step="0.5"
                          placeholder="输入小时数"
                          value={timeAdjustmentInput}
                          onChange={(e) => setTimeAdjustmentInput(e.target.value)}
                          className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <Button
                          size="sm"
                          onClick={handleBatchTimeAdjustment}
                          className="bg-blue-500 hover:bg-blue-600 text-white text-xs h-7 px-3"
                          disabled={!timeAdjustmentInput.trim()}
                        >
                          调整
                        </Button>
                      </div>
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>• 正数增加剩余时间，负数减少剩余时间</div>
                        <div>• 例如：输入 2 增加2小时剩余时间，输入 -1.5 减少1.5小时剩余时间</div>
                        <div>• 直接修改订单的实际剩余时间</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-white rounded-lg shadow-sm border">
              {/* 表头 */}
              <div className="p-4 border-b bg-gray-50">
                <div className="grid grid-cols-12 gap-4 items-center">
                  {/* 复选框列 */}
                  <div className="col-span-1 flex justify-center">
                    <input
                      type="checkbox"
                      checked={selectedOrders.length === getFilteredAndSortedOrders().length && getFilteredAndSortedOrders().length > 0}
                      onChange={handleSelectAll}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                  </div>

                  {/* 序号列 */}
                  <div className="col-span-1 text-center">
                    <span className="text-sm font-medium text-gray-700">#</span>
                  </div>

                  {/* 服务信息列 */}
                  <div className="col-span-3">
                    <span className="text-sm font-medium text-gray-700">服务信息</span>
                  </div>

                  {/* 时间信息列 */}
                  <div className="col-span-3 text-center">
                    <span className="text-sm font-medium text-gray-700">时间状态</span>
                  </div>

                  {/* 状态列 */}
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-700">订单状态</span>
                  </div>

                  {/* 价格列 */}
                  <div className="col-span-1 text-right">
                    <span className="text-sm font-medium text-gray-700">价格</span>
                  </div>

                  {/* 操作列 */}
                  <div className="col-span-1 text-center">
                    <span className="text-sm font-medium text-gray-700">操作</span>
                  </div>
                </div>

                <div className="mt-2 text-right">
                  <span className="text-sm text-gray-500">
                    共 {getFilteredAndSortedOrders().length} 个订单
                  </span>
                </div>
              </div>

              <div className="divide-y">
                {getCurrentPageOrders().map((order) => (
                  <div key={order.id} className="p-6 hover:bg-gray-50">
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* 复选框 - 1列 */}
                      <div className="col-span-1 flex justify-center">
                        <input
                          type="checkbox"
                          checked={selectedOrders.includes(order.id)}
                          onChange={(e) => {
                            e.stopPropagation()
                            handleSelectOrder(order.id)
                          }}
                          className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                        />
                      </div>

                      {/* 序号 - 1列 */}
                      <div className="col-span-1 flex justify-center">
                        <span className="text-sm font-mono text-gray-600 bg-gray-100 px-2 py-1 rounded">
                          #{order.sequence_number}
                        </span>
                      </div>

                      {/* 服务信息 - 3列 */}
                      <div className="col-span-3 flex items-center space-x-4 cursor-pointer" onClick={() => {
                        setScrollPosition(window.scrollY)
                        setSelectedOrder(order)
                        // 进入详情页时滚动到顶部
                        setTimeout(() => {
                          window.scrollTo(0, 0)
                        }, 50)
                      }}>
                        <div className="w-10 h-10 flex items-center justify-center text-2xl">
                          {order.service_type === 'brand' ? '🎨' :
                           order.service_type === 'character' ? '👤' : '🖼️'}
                        </div>
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium truncate">{order.service_name}</h4>
                          <p className="text-sm text-gray-600 truncate">{order.customer_contact}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(order.created_at).toLocaleDateString('zh-CN')}
                          </p>
                          {order.admin_notes && (
                            <div className="relative group mt-2">
                              <div className="bg-yellow-50 border border-yellow-200 rounded px-2 py-1 text-xs text-yellow-800 cursor-help">
                                📝 {order.admin_notes.length > 25 ? `${order.admin_notes.substring(0, 25)}...` : order.admin_notes}
                              </div>
                              <div className="absolute left-0 top-8 z-10 bg-black text-white text-xs rounded px-3 py-2 max-w-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                                {order.admin_notes}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 时间信息 - 3列 */}
                      <div className="col-span-3 flex justify-center">
                        <div className={`text-sm px-3 py-1 rounded-full whitespace-nowrap ${
                          getTimeStatus(order) === 'overdue' ? 'bg-red-100 text-red-700' :
                          getTimeStatus(order) === 'warning' ? 'bg-yellow-100 text-yellow-700' :
                          getTimeStatus(order) === 'completed' ? 'bg-green-100 text-green-700' :
                          'bg-blue-100 text-blue-700'
                        }`}>
                          {order.order_status === 'completed' ? (
                            `用时${Math.round(getRemainingTime(order).totalHours)}小时`
                          ) : (
                            `剩余${formatTimeRemaining(getRemainingTime(order).remainingMs)}`
                          )}
                        </div>
                      </div>

                      {/* 状态 - 2列 */}
                      <div className="col-span-2 flex justify-center">
                        <Badge className={`w-16 text-center ${
                          order.order_status === 'completed' ? 'bg-green-100 text-green-800' :
                          order.order_status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {order.order_status === 'completed' ? '已完成' :
                           order.order_status === 'in_progress' ? '制作中' : '待处理'}
                        </Badge>
                      </div>

                      {/* 价格 - 1列 */}
                      <div className="col-span-1 text-right">
                        <div className="font-bold text-lg">${order.price}</div>
                      </div>

                      {/* 操作 - 1列 */}
                      <div className="col-span-1 flex justify-center">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteOrder(order.id)
                          }}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页控件 */}
              <div className="p-4 border-t bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, getFilteredAndSortedOrders().length)} 条，
                    共 {getFilteredAndSortedOrders().length} 条记录
                  </div>

                  <div className="flex items-center space-x-4">
                    {/* 上一页按钮 */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      上一页
                    </Button>

                    {/* 页码显示和跳转 */}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-700">第</span>
                      <Input
                        type="number"
                        min="1"
                        max={getTotalPages()}
                        value={jumpToPage || currentPage}
                        onChange={(e) => setJumpToPage(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleJumpToPage()
                          }
                        }}
                        onBlur={handleJumpToPage}
                        className="w-16 text-center text-sm"
                      />
                      <span className="text-sm text-gray-700">/ {getTotalPages()} 页</span>
                    </div>

                    {/* 下一页按钮 */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(getTotalPages(), currentPage + 1))}
                      disabled={currentPage === getTotalPages()}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              </div>

            </div>
          </div>
        )

      case 'statistics':
        return (
          <div className="space-y-8">
            {/* 页面标题 */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">🎨 插画工作室数据分析</h1>
                <p className="text-gray-600 mt-1">清晰的业务数据洞察</p>
              </div>
              <div className="text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg">
                更新时间: {new Date().toLocaleString('zh-CN')}
              </div>
            </div>

            {/* 📅 今日数据 (实时更新) */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📅 今日数据 (实时更新)</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {/* 今日订单量 */}
                <div className="bg-white p-4 rounded-lg border shadow-sm">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {orders.filter(order => order.created_at.startsWith(new Date().toISOString().split('T')[0])).length}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">今日订单</p>
                    <p className="text-xs text-gray-500">单</p>
                  </div>
                </div>

                {/* 今日收入 */}
                <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200 shadow-sm">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-700">
                      ${orders.filter(order => order.payment_status === 'completed' && order.created_at.startsWith(new Date().toISOString().split('T')[0])).reduce((sum, order) => sum + order.price, 0)}
                    </p>
                    <p className="text-sm text-green-700 font-medium">今日收入</p>
                    <p className="text-xs text-green-600">已确认收款</p>
                  </div>
                </div>

                {/* 今日品牌插画 */}
                <div className="bg-white p-4 rounded-lg border shadow-sm">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">
                      {orders.filter(order => order.service_type === 'brand' && order.created_at.startsWith(new Date().toISOString().split('T')[0])).length}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">品牌插画</p>
                    <p className="text-xs text-gray-500">🎨</p>
                  </div>
                </div>

                {/* 今日角色设计 */}
                <div className="bg-white p-4 rounded-lg border shadow-sm">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-indigo-600">
                      {orders.filter(order => order.service_type === 'character' && order.created_at.startsWith(new Date().toISOString().split('T')[0])).length}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">角色设计</p>
                    <p className="text-xs text-gray-500">👤</p>
                  </div>
                </div>

                {/* 今日场景插画 */}
                <div className="bg-white p-4 rounded-lg border shadow-sm">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-teal-600">
                      {orders.filter(order => order.service_type === 'scene' && order.created_at.startsWith(new Date().toISOString().split('T')[0])).length}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">场景插画</p>
                    <p className="text-xs text-gray-500">🖼️</p>
                  </div>
                </div>
              </div>

              {/* 今日完成情况 */}
              <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    今日完成: <span className="font-bold text-green-600">
                      {orders.filter(order => order.order_status === 'completed' && order.status_updated_at && order.status_updated_at.startsWith(new Date().toISOString().split('T')[0])).length}
                    </span> 单
                  </span>
                  <span className="text-gray-600">
                    当前待处理: <span className="font-bold text-orange-600">
                      {orders.filter(order => order.order_status === 'pending').length}
                    </span> 单
                  </span>
                </div>
              </div>
            </div>

            {/* 📊 本月数据 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">📊 本月数据</h2>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

                {/* 本月总订单 */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">总订单概况</h3>
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-3xl font-bold text-blue-600">
                        {(() => {
                          const now = new Date()
                          const currentMonth = now.getMonth()
                          const currentYear = now.getFullYear()
                          return orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                          }).length
                        })()}
                      </p>
                      <p className="text-sm text-gray-600 font-medium">本月总订单</p>
                    </div>

                    <div className="pt-4 border-t">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">完成率</span>
                        <span className="text-sm font-bold text-green-600">
                          {(() => {
                            const now = new Date()
                            const currentMonth = now.getMonth()
                            const currentYear = now.getFullYear()
                            const thisMonthOrders = orders.filter(order => {
                              const orderDate = new Date(order.created_at)
                              return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                            })
                            const completed = thisMonthOrders.filter(order => order.order_status === 'completed').length
                            return thisMonthOrders.length > 0 ? Math.round((completed / thisMonthOrders.length) * 100) : 0
                          })()}%
                        </span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${(() => {
                              const now = new Date()
                              const currentMonth = now.getMonth()
                              const currentYear = now.getFullYear()
                              const thisMonthOrders = orders.filter(order => {
                                const orderDate = new Date(order.created_at)
                                return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                              })
                              const completed = thisMonthOrders.filter(order => order.order_status === 'completed').length
                              return thisMonthOrders.length > 0 ? Math.round((completed / thisMonthOrders.length) * 100) : 0
                            })()}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 本月服务类型占比 */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">服务类型占比</h3>
                  <div className="space-y-3">
                    {[
                      { type: 'brand', name: '品牌插画', icon: '🎨', color: 'purple' },
                      { type: 'character', name: '角色设计', icon: '👤', color: 'indigo' },
                      { type: 'scene', name: '场景插画', icon: '🖼️', color: 'teal' }
                    ].map((service) => {
                      const now = new Date()
                      const currentMonth = now.getMonth()
                      const currentYear = now.getFullYear()
                      const thisMonthOrders = orders.filter(order => {
                        const orderDate = new Date(order.created_at)
                        return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                      })
                      const serviceOrders = thisMonthOrders.filter(order => order.service_type === service.type)
                      const percentage = thisMonthOrders.length > 0 ? Math.round((serviceOrders.length / thisMonthOrders.length) * 100) : 0

                      return (
                        <div key={service.type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <span className="text-xl mr-3">{service.icon}</span>
                            <span className="text-sm font-medium text-gray-700">{service.name}</span>
                          </div>
                          <div className="text-right">
                            <span className={`text-lg font-bold text-${service.color}-600`}>
                              {serviceOrders.length}
                            </span>
                            <span className="text-sm text-gray-500 ml-1">({percentage}%)</span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* 本月每日趋势 */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">每日趋势 (30天)</h3>

                  {/* 趋势图 */}
                  <div className="mb-4">
                    <div className="flex items-end justify-between h-24 bg-gray-50 rounded-lg p-2">
                      {(() => {
                        const last30Days = []
                        for (let i = 29; i >= 0; i--) {
                          const date = new Date()
                          date.setDate(date.getDate() - i)
                          const dateStr = date.toISOString().split('T')[0]
                          const dayOrders = orders.filter(order => order.created_at.startsWith(dateStr))
                          last30Days.push(dayOrders.length)
                        }

                        const maxOrders = Math.max(...last30Days, 1)

                        return last30Days.map((orderCount, index) => (
                          <div key={index} className="flex flex-col items-center">
                            <div
                              className="bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                              style={{
                                height: `${(orderCount / maxOrders) * 60}px`,
                                width: '4px',
                                minHeight: orderCount > 0 ? '2px' : '0px'
                              }}
                              title={`${orderCount}单`}
                            ></div>
                          </div>
                        ))
                      })()}
                    </div>
                  </div>

                  {/* 统计数据 */}
                  <div className="text-center">
                    <p className="text-lg font-bold text-gray-900">
                      {(() => {
                        const last30Days = []
                        for (let i = 29; i >= 0; i--) {
                          const date = new Date()
                          date.setDate(date.getDate() - i)
                          const dateStr = date.toISOString().split('T')[0]
                          const dayOrders = orders.filter(order => order.created_at.startsWith(dateStr))
                          last30Days.push(dayOrders.length)
                        }
                        return Math.max(...last30Days)
                      })()}
                    </p>
                    <p className="text-sm text-gray-600">最高单日订单</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 🔄 本月 vs 上月对比分析 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">🔄 本月 vs 上月对比分析</h2>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

                {/* 订单量变化 */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">订单量变化</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <span className="text-sm font-medium text-gray-700">本月</span>
                      <span className="text-xl font-bold text-blue-600">
                        {(() => {
                          const now = new Date()
                          const currentMonth = now.getMonth()
                          const currentYear = now.getFullYear()
                          return orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                          }).length
                        })()}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm font-medium text-gray-700">上月</span>
                      <span className="text-xl font-bold text-gray-600">
                        {(() => {
                          const now = new Date()
                          const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
                          const lastYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()
                          return orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === lastMonth && orderDate.getFullYear() === lastYear
                          }).length
                        })()}
                      </span>
                    </div>

                    <div className="text-center pt-2 border-t">
                      {(() => {
                        const now = new Date()
                        const currentMonth = now.getMonth()
                        const currentYear = now.getFullYear()
                        const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
                        const lastYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()

                        const thisMonthOrders = orders.filter(order => {
                          const orderDate = new Date(order.created_at)
                          return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                        }).length

                        const lastMonthOrders = orders.filter(order => {
                          const orderDate = new Date(order.created_at)
                          return orderDate.getMonth() === lastMonth && orderDate.getFullYear() === lastYear
                        }).length

                        const growthRate = lastMonthOrders > 0
                          ? Math.round(((thisMonthOrders - lastMonthOrders) / lastMonthOrders) * 100)
                          : thisMonthOrders > 0 ? 100 : 0

                        return (
                          <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-bold ${
                            growthRate > 0
                              ? 'bg-green-100 text-green-800'
                              : growthRate < 0
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                          }`}>
                            {growthRate > 0 ? '↗️' : growthRate < 0 ? '↘️' : '➡️'}
                            {growthRate >= 0 ? '+' : ''}{growthRate}%
                          </div>
                        )
                      })()}
                    </div>
                  </div>
                </div>

                {/* 服务类型变化 */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">服务类型变化</h3>
                  <div className="space-y-3">
                    {[
                      { type: 'brand', name: '品牌插画', icon: '🎨' },
                      { type: 'character', name: '角色设计', icon: '👤' },
                      { type: 'scene', name: '场景插画', icon: '🖼️' }
                    ].map((service) => {
                      const now = new Date()
                      const currentMonth = now.getMonth()
                      const currentYear = now.getFullYear()
                      const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
                      const lastYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()

                      const thisMonthService = orders.filter(order => {
                        const orderDate = new Date(order.created_at)
                        return order.service_type === service.type &&
                               orderDate.getMonth() === currentMonth &&
                               orderDate.getFullYear() === currentYear
                      }).length

                      const lastMonthService = orders.filter(order => {
                        const orderDate = new Date(order.created_at)
                        return order.service_type === service.type &&
                               orderDate.getMonth() === lastMonth &&
                               orderDate.getFullYear() === lastYear
                      }).length

                      const changeRate = lastMonthService > 0
                        ? Math.round(((thisMonthService - lastMonthService) / lastMonthService) * 100)
                        : thisMonthService > 0 ? 100 : 0

                      return (
                        <div key={service.type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <span className="text-lg mr-2">{service.icon}</span>
                            <span className="text-sm font-medium text-gray-700">{service.name}</span>
                          </div>
                          <div className={`text-sm font-bold ${
                            changeRate > 0 ? 'text-green-600' : changeRate < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {changeRate > 0 ? '↗️' : changeRate < 0 ? '↘️' : '➡️'}
                            {changeRate >= 0 ? '+' : ''}{changeRate}%
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* 效率指标 */}
                <div className="bg-white p-6 rounded-lg border shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">效率指标</h3>
                  <div className="space-y-4">

                    {/* 完成率变化 */}
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">完成率变化</span>
                        {(() => {
                          const now = new Date()
                          const currentMonth = now.getMonth()
                          const currentYear = now.getFullYear()
                          const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
                          const lastYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()

                          const thisMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                          })
                          const thisMonthCompleted = thisMonthOrders.filter(order => order.order_status === 'completed').length
                          const thisMonthRate = thisMonthOrders.length > 0 ? Math.round((thisMonthCompleted / thisMonthOrders.length) * 100) : 0

                          const lastMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === lastMonth && orderDate.getFullYear() === lastYear
                          })
                          const lastMonthCompleted = lastMonthOrders.filter(order => order.order_status === 'completed').length
                          const lastMonthRate = lastMonthOrders.length > 0 ? Math.round((lastMonthCompleted / lastMonthOrders.length) * 100) : 0

                          const rateChange = thisMonthRate - lastMonthRate

                          return (
                            <span className={`text-sm font-bold ${
                              rateChange > 0 ? 'text-green-600' : rateChange < 0 ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {rateChange > 0 ? '↗️' : rateChange < 0 ? '↘️' : '➡️'}
                              {rateChange >= 0 ? '+' : ''}{rateChange}%
                            </span>
                          )
                        })()}
                      </div>
                      <div className="text-xs text-gray-500">
                        本月: {(() => {
                          const now = new Date()
                          const currentMonth = now.getMonth()
                          const currentYear = now.getFullYear()
                          const thisMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                          })
                          const thisMonthCompleted = thisMonthOrders.filter(order => order.order_status === 'completed').length
                          return thisMonthOrders.length > 0 ? Math.round((thisMonthCompleted / thisMonthOrders.length) * 100) : 0
                        })()}% | 上月: {(() => {
                          const now = new Date()
                          const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
                          const lastYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()
                          const lastMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === lastMonth && orderDate.getFullYear() === lastYear
                          })
                          const lastMonthCompleted = lastMonthOrders.filter(order => order.order_status === 'completed').length
                          return lastMonthOrders.length > 0 ? Math.round((lastMonthCompleted / lastMonthOrders.length) * 100) : 0
                        })()}%
                      </div>
                    </div>

                    {/* 日均订单变化 */}
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">日均订单变化</span>
                        {(() => {
                          const now = new Date()
                          const currentMonth = now.getMonth()
                          const currentYear = now.getFullYear()
                          const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
                          const lastYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()

                          const thisMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                          }).length
                          const thisMonthDays = new Date().getDate() // 本月已过天数
                          const thisMonthDaily = thisMonthDays > 0 ? (thisMonthOrders / thisMonthDays).toFixed(1) : '0'

                          const lastMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === lastMonth && orderDate.getFullYear() === lastYear
                          }).length
                          const lastMonthDays = new Date(lastYear, lastMonth + 1, 0).getDate() // 上月总天数
                          const lastMonthDaily = lastMonthDays > 0 ? (lastMonthOrders / lastMonthDays).toFixed(1) : '0'

                          const dailyChange = lastMonthDaily > 0
                            ? Math.round(((parseFloat(thisMonthDaily) - parseFloat(lastMonthDaily)) / parseFloat(lastMonthDaily)) * 100)
                            : parseFloat(thisMonthDaily) > 0 ? 100 : 0

                          return (
                            <span className={`text-sm font-bold ${
                              dailyChange > 0 ? 'text-green-600' : dailyChange < 0 ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {dailyChange > 0 ? '↗️' : dailyChange < 0 ? '↘️' : '➡️'}
                              {dailyChange >= 0 ? '+' : ''}{dailyChange}%
                            </span>
                          )
                        })()}
                      </div>
                      <div className="text-xs text-gray-500">
                        本月: {(() => {
                          const now = new Date()
                          const currentMonth = now.getMonth()
                          const currentYear = now.getFullYear()
                          const thisMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
                          }).length
                          const thisMonthDays = new Date().getDate()
                          return thisMonthDays > 0 ? (thisMonthOrders / thisMonthDays).toFixed(1) : '0'
                        })()} 单/天 | 上月: {(() => {
                          const now = new Date()
                          const lastMonth = now.getMonth() === 0 ? 11 : now.getMonth() - 1
                          const lastYear = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()
                          const lastMonthOrders = orders.filter(order => {
                            const orderDate = new Date(order.created_at)
                            return orderDate.getMonth() === lastMonth && orderDate.getFullYear() === lastYear
                          }).length
                          const lastMonthDays = new Date(lastYear, lastMonth + 1, 0).getDate()
                          return lastMonthDays > 0 ? (lastMonthOrders / lastMonthDays).toFixed(1) : '0'
                        })()} 单/天
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>
        )

      case 'search':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">订单查询</h2>

            <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
              <div className="flex gap-4">
                <Input
                  placeholder="输入订单ID或PayPal交易ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1"
                />
                <Button onClick={handleSearch}>
                  <Search className="h-4 w-4 mr-2" />
                  查询
                </Button>
              </div>

              <div className="mt-4 text-sm text-gray-600">
                <p>支持查询：</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>订单ID（如：ORD-20241201-ABC123）</li>
                  <li>PayPal交易ID（如：PAYPAL-TXN-123456）</li>
                </ul>
              </div>
            </div>

            {searchResult && (
              <div className="bg-white rounded-lg shadow-sm border">
                {searchResult === 'not_found' ? (
                  <div className="p-8 text-center">
                    <div className="text-gray-400 text-4xl mb-4">🔍</div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">未找到订单</h3>
                    <p className="text-gray-600">请检查订单ID或PayPal交易ID是否正确</p>
                  </div>
                ) : (
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h3 className="text-xl font-bold">{searchResult.service_name}</h3>
                        <p className="text-gray-600">订单 #{searchResult.order_id}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-green-600">${searchResult.price}</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="font-semibold mb-3">客户信息</h4>
                        <p className="text-gray-600 mb-2">📧 {searchResult.customer_contact}</p>
                        <p className="text-gray-600">📅 {new Date(searchResult.created_at).toLocaleDateString('zh-CN')}</p>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3">订单状态</h4>
                        <div className="space-y-2">
                          <Badge className={
                            searchResult.order_status === 'completed' ? 'bg-green-100 text-green-800' :
                            searchResult.order_status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }>
                            {searchResult.order_status === 'completed' ? '已完成' :
                             searchResult.order_status === 'in_progress' ? '进行中' : '待处理'}
                          </Badge>
                          {searchResult.paypal_transaction_id && (
                            <div>
                              <p className="text-sm text-gray-600">PayPal交易ID:</p>
                              <p className="font-mono text-sm">{searchResult.paypal_transaction_id}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3">需求描述</h4>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="whitespace-pre-line text-gray-700">
                          {searchResult.requirements}
                        </p>
                      </div>
                    </div>

                    <div className="mt-6 flex gap-3">
                      <Button onClick={() => {
                        setScrollPosition(window.scrollY)
                        setSelectedOrder(searchResult)
                        // 进入详情页时滚动到顶部
                        setTimeout(() => {
                          window.scrollTo(0, 0)
                        }, 50)
                      }}>
                        查看完整详情
                      </Button>
                      <Button variant="outline">
                        更新状态
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {!searchResult && (
              <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
                <div className="text-gray-400 text-4xl mb-4">🔍</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">开始查询</h3>
                <p className="text-gray-600">在上方输入框中输入订单ID或PayPal交易ID进行查询</p>
              </div>
            )}
          </div>
        )

      case 'trash':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">回收站</h2>

            <div className="bg-white rounded-lg shadow-sm border">
              {/* 表头 */}
              <div className="p-4 border-b bg-gray-50">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-700">已删除的订单</h3>
                  <span className="text-sm text-gray-500">
                    共 {deletedOrders.length} 个订单
                  </span>
                </div>
              </div>

              {deletedOrders.length === 0 ? (
                <div className="p-8 text-center">
                  <div className="text-gray-400 text-4xl mb-4">🗑️</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">回收站为空</h3>
                  <p className="text-gray-600">删除的订单会出现在这里</p>
                </div>
              ) : (
                <div className="divide-y">
                  {deletedOrders.map((order) => (
                    <div key={order.id} className="p-6 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="text-sm font-mono text-gray-600 bg-gray-100 px-2 py-1 rounded">
                            #{order.sequence_number}
                          </div>
                          <div className="w-10 h-10 flex items-center justify-center text-2xl">
                            {order.service_type === 'brand' ? '🎨' :
                             order.service_type === 'character' ? '👤' : '🖼️'}
                          </div>
                          <div>
                            <h4 className="font-medium">{order.service_name}</h4>
                            <p className="text-sm text-gray-600">{order.customer_contact}</p>
                            <p className="text-xs text-gray-500">
                              删除时间: {new Date(order.deleted_at).toLocaleString('zh-CN')}
                            </p>
                            {order.admin_notes && (
                              <div className="relative group mt-2">
                                <div className="bg-yellow-50 border border-yellow-200 rounded px-2 py-1 text-xs text-yellow-800 cursor-help">
                                  📝 {order.admin_notes.length > 25 ? `${order.admin_notes.substring(0, 25)}...` : order.admin_notes}
                                </div>
                                <div className="absolute left-0 top-8 z-10 bg-black text-white text-xs rounded px-3 py-2 max-w-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                                  {order.admin_notes}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Badge className={`${
                            order.order_status === 'completed' ? 'bg-green-100 text-green-800' :
                            order.order_status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {order.order_status === 'completed' ? '已完成' :
                             order.order_status === 'in_progress' ? '制作中' : '待处理'}
                          </Badge>
                          <span className="font-bold text-lg">${order.price}</span>

                          <div className="flex space-x-2 ml-4">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRestoreOrder(order.id)}
                              className="text-green-600 hover:text-green-700 hover:bg-green-50"
                            >
                              恢复
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handlePermanentDelete(order.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              永久删除
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  // 订单详情弹窗
  if (selectedOrder) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* 菜单按钮 */}
        <div className="fixed top-4 left-4 z-30">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSidebarOpen(true)}
            className="bg-white shadow-md"
          >
            <Menu className="h-4 w-4" />
          </Button>
        </div>

        {/* 淡入淡出侧边栏 */}
        <div className={`fixed inset-0 z-40 transition-opacity duration-300 ${
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}>
          {/* 遮罩层 */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setSidebarOpen(false)}
          />

          {/* 侧边栏 */}
          <div className={`absolute left-0 top-0 h-full w-80 bg-white shadow-2xl transform transition-all duration-300 ease-out ${
            sidebarOpen ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'
          }`}>
            <div className="p-6 border-b flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50">
              <div>
                <h2 className="text-lg font-bold text-gray-900">🎨 导航菜单</h2>
                <p className="text-sm text-gray-600">选择功能模块</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSidebarOpen(false)}
                className="hover:bg-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <nav className="p-6">
              <div className="space-y-2">
                {menuItems.map((item, index) => {
                  const Icon = item.icon
                  return (
                    <button
                      key={item.id}
                      onClick={() => {
                        setActiveTab(item.id)
                        setSelectedOrder(null)
                        setTimeout(() => setSidebarOpen(false), 150)
                      }}
                      className={`w-full flex items-center space-x-4 px-4 py-4 rounded-xl text-left transition-all duration-200 transform hover:scale-105 ${
                        activeTab === item.id
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg'
                          : 'text-gray-700 hover:bg-gray-50 hover:shadow-md'
                      }`}
                      style={{
                        animationDelay: `${index * 50}ms`
                      }}
                    >
                      <div className={`p-2 rounded-lg ${
                        activeTab === item.id
                          ? 'bg-white bg-opacity-20'
                          : 'bg-gray-100'
                      }`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <span className="font-medium">{item.label}</span>
                        <p className={`text-xs ${
                          activeTab === item.id ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          {item.id === 'orders' && '管理所有订单'}
                          {item.id === 'statistics' && '数据分析统计'}
                          {item.id === 'search' && '快速查询订单'}
                        </p>
                      </div>
                    </button>
                  )
                })}
              </div>
            </nav>

            <div className="absolute bottom-0 left-0 right-0 p-6 border-t bg-gray-50">
              <div className="text-center text-sm text-gray-500">
                <p>插画工作室管理系统</p>
                <p className="text-xs mt-1">v1.0.0</p>
              </div>
            </div>
          </div>
        </div>

        {/* 订单详情内容 */}
        <div className="p-8">
          <Button
            onClick={() => {
              setSelectedOrder(null)
              // 延迟恢复滚动位置，确保页面已渲染
              setTimeout(() => {
                window.scrollTo(0, scrollPosition)
              }, 100)
            }}
            className="mb-6"
            variant="outline"
          >
            ← 返回订单列表
          </Button>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <span className="text-sm font-mono text-gray-600 bg-gray-100 px-2 py-1 rounded">
                    #{selectedOrder.sequence_number}
                  </span>
                  <h1 className="text-2xl font-bold">{selectedOrder.service_name}</h1>
                </div>
                <p className="text-gray-600">订单 #{selectedOrder.order_id}</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">${selectedOrder.price}</div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3">客户信息</h3>
                <p className="text-gray-600 mb-2">📧 {selectedOrder.customer_contact}</p>
                <p className="text-gray-600">📅 {new Date(selectedOrder.created_at).toLocaleDateString('zh-CN')}</p>
              </div>

              <div>
                <h3 className="font-semibold mb-3">订单状态</h3>
                <Badge className={
                  selectedOrder.order_status === 'completed' ? 'bg-green-100 text-green-800' :
                  selectedOrder.order_status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }>
                  {selectedOrder.order_status === 'completed' ? '已完成' :
                   selectedOrder.order_status === 'in_progress' ? '进行中' : '待处理'}
                </Badge>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="font-semibold mb-3">需求描述</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="whitespace-pre-line text-gray-700">
                  {selectedOrder.requirements}
                </p>
              </div>
            </div>

            {/* 管理员备注 */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold">管理员备注</h3>
                {!editingNotes && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleStartEditNotes}
                  >
                    {selectedOrder.admin_notes ? '编辑备注' : '添加备注'}
                  </Button>
                )}
              </div>

              {editingNotes ? (
                <div className="space-y-3">
                  <textarea
                    value={tempNotes}
                    onChange={(e) => setTempNotes(e.target.value)}
                    placeholder="输入管理员备注..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={handleSaveNotes}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      保存备注
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEditNotes}
                    >
                      取消
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                  {selectedOrder.admin_notes ? (
                    <p className="whitespace-pre-line text-gray-700">
                      {selectedOrder.admin_notes}
                    </p>
                  ) : (
                    <p className="text-gray-500 italic">暂无备注</p>
                  )}
                </div>
              )}
            </div>

            {/* 客户照片 */}
            {selectedOrder.customer_photos && selectedOrder.customer_photos.length > 0 && (
              <div className="mt-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">客户照片</h3>
                  {selectedOrder.order_status === 'in_progress' || selectedOrder.order_status === 'completed' ? (
                    <Button size="sm" variant="outline">
                      下载客户照片
                    </Button>
                  ) : (
                    <span className="text-sm text-gray-500">仅预览</span>
                  )}
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {selectedOrder.customer_photos.map((image: string, index: number) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`客户照片 ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg border shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => window.open(image, '_blank')}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all flex items-center justify-center">
                        <span className="text-white opacity-0 group-hover:opacity-100 text-sm">
                          {selectedOrder.order_status === 'pending' ? '预览图片' : '点击查看大图'}
                        </span>
                      </div>
                      {(selectedOrder.order_status === 'in_progress' || selectedOrder.order_status === 'completed') && (
                        <div className="absolute top-2 right-2">
                          <Button size="sm" variant="outline" className="bg-white bg-opacity-90 text-xs px-2 py-1">
                            下载
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 风格参考图 */}
            {selectedOrder.style_references && selectedOrder.style_references.length > 0 && (
              <div className="mt-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">风格参考图</h3>
                  {selectedOrder.order_status === 'in_progress' || selectedOrder.order_status === 'completed' ? (
                    <Button size="sm" variant="outline">
                      下载风格参考
                    </Button>
                  ) : (
                    <span className="text-sm text-gray-500">仅预览</span>
                  )}
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {selectedOrder.style_references.map((image: string, index: number) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`风格参考 ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg border shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => window.open(image, '_blank')}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all flex items-center justify-center">
                        <span className="text-white opacity-0 group-hover:opacity-100 text-sm">
                          {selectedOrder.order_status === 'pending' ? '预览图片' : '点击查看大图'}
                        </span>
                      </div>
                      {(selectedOrder.order_status === 'in_progress' || selectedOrder.order_status === 'completed') && (
                        <div className="absolute top-2 right-2">
                          <Button size="sm" variant="outline" className="bg-white bg-opacity-90 text-xs px-2 py-1">
                            下载
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 完成作品图片 - 只有已完成状态才显示 */}
            {selectedOrder.order_status === 'completed' && selectedOrder.completed_images && selectedOrder.completed_images.length > 0 && (
              <div className="mt-6">
                <h3 className="font-semibold mb-3 flex items-center">
                  <span>完成作品</span>
                  <span className="ml-2 text-green-600">✓</span>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedOrder.completed_images.map((image: string, index: number) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`完成作品 ${index + 1}`}
                        className="w-full h-48 object-cover rounded-lg border shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => window.open(image, '_blank')}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all flex items-center justify-center">
                        <span className="text-white opacity-0 group-hover:opacity-100 text-sm">点击查看大图</span>
                      </div>
                      <div className="absolute top-2 right-2">
                        <Button size="sm" variant="outline" className="bg-white bg-opacity-90">
                          下载
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 制作中状态的上传区域 */}
            {selectedOrder.order_status === 'in_progress' && (
              <div className="mt-6">
                <h3 className="font-semibold mb-3">上传完成作品</h3>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                  <div className="text-gray-400 mb-2 text-4xl">📁</div>
                  <p className="text-gray-600 mb-2">点击上传或拖拽文件到此处</p>
                  <p className="text-sm text-gray-500 mb-4">支持 JPG, PNG, PDF 格式</p>
                  <Button
                    className="mt-2"
                    onClick={() => {
                      // 模拟上传完成
                      const mockImages = [
                        'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600',
                        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600'
                      ]
                      handleUploadComplete(selectedOrder.id, mockImages)
                    }}
                  >
                    选择文件上传
                  </Button>
                  <p className="text-xs text-gray-400 mt-2">上传完成后订单状态将自动变为"已完成"</p>
                </div>
              </div>
            )}

            <div className="mt-6 flex gap-3">
              {selectedOrder.order_status === 'pending' && (
                <Button
                  onClick={() => handleStartProcessing(selectedOrder.id)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  开始处理
                </Button>
              )}

              {selectedOrder.order_status === 'in_progress' && (
                <Button variant="outline" disabled>
                  制作中...
                </Button>
              )}

              {selectedOrder.order_status === 'completed' && (
                <Button variant="outline" className="text-green-600 border-green-600">
                  ✓ 已完成
                </Button>
              )}

              <Button variant="outline">发送消息</Button>

              {(selectedOrder.order_status === 'in_progress' || selectedOrder.order_status === 'completed') && (
                <Button variant="outline">下载所有文件</Button>
              )}
            </div>

            {/* 底部返回按钮 */}
            <div className="mt-8 pt-6 border-t border-gray-200 text-center">
              <Button
                onClick={() => {
                  setSelectedOrder(null)
                  setTimeout(() => {
                    window.scrollTo(0, scrollPosition)
                  }, 100)
                }}
                variant="outline"
                className="px-8"
              >
                ← 返回订单列表
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 菜单按钮 */}
      <div className="fixed top-4 left-4 z-30">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setSidebarOpen(true)}
          className="bg-white shadow-md"
        >
          <Menu className="h-4 w-4" />
        </Button>
      </div>

      {/* 淡入淡出侧边栏 */}
      <div className={`fixed inset-0 z-40 transition-opacity duration-300 ${
        sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}>
        {/* 遮罩层 */}
        <div
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={() => setSidebarOpen(false)}
        />

        {/* 侧边栏 */}
        <div className={`absolute left-0 top-0 h-full w-80 bg-white shadow-2xl transform transition-all duration-300 ease-out ${
          sidebarOpen ? 'translate-x-0 opacity-100' : '-translate-x-full opacity-0'
        }`}>
          <div className="p-6 border-b flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50">
            <div>
              <h2 className="text-lg font-bold text-gray-900">🎨 导航菜单</h2>
              <p className="text-sm text-gray-600">选择功能模块</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSidebarOpen(false)}
              className="hover:bg-white"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <nav className="p-6">
            <div className="space-y-2">
              {menuItems.map((item, index) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      setActiveTab(item.id)
                      setTimeout(() => setSidebarOpen(false), 150)
                    }}
                    className={`w-full flex items-center space-x-4 px-4 py-4 rounded-xl text-left transition-all duration-200 transform hover:scale-105 ${
                      activeTab === item.id
                        ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg'
                        : 'text-gray-700 hover:bg-gray-50 hover:shadow-md'
                    }`}
                    style={{
                      animationDelay: `${index * 50}ms`
                    }}
                  >
                    <div className={`p-2 rounded-lg ${
                      activeTab === item.id
                        ? 'bg-white bg-opacity-20'
                        : 'bg-gray-100'
                    }`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <span className="font-medium">{item.label}</span>
                      <p className={`text-xs ${
                        activeTab === item.id ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {item.id === 'orders' && '管理所有订单'}
                        {item.id === 'statistics' && '数据分析统计'}
                        {item.id === 'search' && '快速查询订单'}
                      </p>
                    </div>
                  </button>
                )
              })}
            </div>
          </nav>

          <div className="absolute bottom-0 left-0 right-0 p-6 border-t bg-gray-50">
            <div className="text-center text-sm text-gray-500">
              <p>插画工作室管理系统</p>
              <p className="text-xs mt-1">v1.0.0</p>
            </div>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="p-8">
        {renderContent()}
      </div>
    </div>
  )
}
