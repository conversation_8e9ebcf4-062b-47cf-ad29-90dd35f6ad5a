"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminDashboard() {\n    var _menuItems_find;\n    _s();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"orders\");\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchOrders = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/orders\");\n            const data = await response.json();\n            if (data.success) {\n                // 为订单添加序号和其他必要字段\n                const ordersWithExtras = data.data.orders.map((order, index)=>({\n                        ...order,\n                        sequence_number: index + 1,\n                        status_updated_at: order.updated_at,\n                        reference_images: order.style_images || [],\n                        completed_images: order.order_status === \"completed\" ? [\n                            \"https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600\"\n                        ] : [],\n                        admin_notes: \"\"\n                    }));\n                setOrders(ordersWithExtras);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch orders:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchOrders();\n    }, []);\n    // 每分钟更新一次时间，保持倒计时准确\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000) // 每分钟更新\n        ;\n        return ()=>clearInterval(timer);\n    }, []);\n    const menuItems = [\n        {\n            id: \"orders\",\n            label: \"订单\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: \"statistics\",\n            label: \"数据统计\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"search\",\n            label: \"查询\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: \"trash\",\n            label: \"回收站\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    // 根据价格确定处理小时数\n    const getProcessingHours = (price)=>{\n        if (price === 5) return 48 // 48小时\n        ;\n        if (price === 7) return 72 // 72小时\n        ;\n        if (price === 10) return 96 // 96小时\n        ;\n        return 48 // 默认\n        ;\n    };\n    // 制作时间固定2小时\n    const PRODUCTION_HOURS = 2;\n    // 计算剩余时间\n    const getRemainingTime = (order)=>{\n        const now = currentTime;\n        const createdAt = new Date(order.created_at);\n        const statusUpdatedAt = new Date(order.status_updated_at);\n        // 获取该订单的时间调整（以小时为单位）\n        const timeAdjustmentHours = timeAdjustments[order.id] || 0;\n        if (order.order_status === \"pending\") {\n            // 待处理：从创建时间开始计算\n            const processingHours = getProcessingHours(order.price);\n            const deadlineTime = new Date(createdAt.getTime() + processingHours * 60 * 60 * 1000);\n            const baseRemainingMs = deadlineTime.getTime() - now.getTime();\n            // 直接在剩余时间上加上调整量\n            const adjustedRemainingMs = baseRemainingMs + timeAdjustmentHours * 60 * 60 * 1000;\n            return {\n                remainingMs: adjustedRemainingMs,\n                totalHours: processingHours,\n                type: \"processing\"\n            };\n        } else if (order.order_status === \"in_progress\") {\n            // 制作中：从状态更新时间开始计算2小时\n            const deadlineTime = new Date(statusUpdatedAt.getTime() + PRODUCTION_HOURS * 60 * 60 * 1000);\n            const baseRemainingMs = deadlineTime.getTime() - now.getTime();\n            // 直接在剩余时间上加上调整量\n            const adjustedRemainingMs = baseRemainingMs + timeAdjustmentHours * 60 * 60 * 1000;\n            return {\n                remainingMs: adjustedRemainingMs,\n                totalHours: PRODUCTION_HOURS,\n                type: \"production\"\n            };\n        } else {\n            // 已完成：计算实际用时\n            const totalMs = statusUpdatedAt.getTime() - createdAt.getTime();\n            return {\n                remainingMs: 0,\n                totalHours: totalMs / (1000 * 60 * 60),\n                type: \"completed\"\n            };\n        }\n    };\n    // 格式化时间显示\n    const formatTimeRemaining = (remainingMs)=>{\n        if (remainingMs <= 0) return \"已超时\";\n        const hours = Math.floor(remainingMs / (1000 * 60 * 60));\n        const minutes = Math.floor(remainingMs % (1000 * 60 * 60) / (1000 * 60));\n        if (hours > 0) {\n            return \"\".concat(hours, \"小时\").concat(minutes, \"分钟\");\n        } else {\n            return \"\".concat(minutes, \"分钟\");\n        }\n    };\n    // 获取时间状态颜色\n    const getTimeStatus = (order)=>{\n        const { remainingMs, totalHours } = getRemainingTime(order);\n        if (order.order_status === \"completed\") {\n            return \"completed\";\n        }\n        if (remainingMs <= 0) {\n            return \"overdue\" // 已超时\n            ;\n        }\n        const totalMs = totalHours * 60 * 60 * 1000;\n        const remainingPercent = remainingMs / totalMs;\n        if (remainingPercent < 0.25) {\n            return \"warning\" // 即将到期\n            ;\n        }\n        return \"normal\" // 正常\n        ;\n    };\n    // 批量选择功能\n    const handleSelectOrder = (orderId)=>{\n        setSelectedOrders((prev)=>{\n            if (prev.includes(orderId)) {\n                return prev.filter((id)=>id !== orderId);\n            } else {\n                return [\n                    ...prev,\n                    orderId\n                ];\n            }\n        });\n    };\n    // 批量调整时间 - 记录调整量\n    const handleBatchTimeAdjustment = ()=>{\n        const hours = parseFloat(timeAdjustmentInput);\n        if (isNaN(hours) || hours === 0) {\n            alert(\"请输入有效的时间数值（可以是负数）\");\n            return;\n        }\n        // 记录每个选中订单的时间调整\n        setTimeAdjustments((prev)=>{\n            const newAdjustments = {\n                ...prev\n            };\n            selectedOrders.forEach((orderId)=>{\n                const currentAdjustment = newAdjustments[orderId] || 0;\n                newAdjustments[orderId] = currentAdjustment + hours;\n            });\n            return newAdjustments;\n        });\n        // 清空输入框\n        setTimeAdjustmentInput(\"\");\n    };\n    // 全选/取消全选\n    const handleSelectAll = ()=>{\n        const filteredOrders = getFilteredAndSortedOrders();\n        if (selectedOrders.length === filteredOrders.length && filteredOrders.length > 0) {\n            setSelectedOrders([]);\n        } else {\n            setSelectedOrders(filteredOrders.map((order)=>order.id));\n        }\n    };\n    // 批量状态更新\n    const handleBatchStatusChange = async (newStatus)=>{\n        try {\n            // 这里应该调用API更新状态，现在先本地更新\n            setOrders((prevOrders)=>prevOrders.map((order)=>selectedOrders.includes(order.id) ? {\n                        ...order,\n                        order_status: newStatus,\n                        status_updated_at: new Date().toISOString()\n                    } : order));\n            setSelectedOrders([]);\n        } catch (error) {\n            console.error(\"Failed to update order status:\", error);\n        }\n    };\n    // 批量删除\n    const handleBatchDelete = async ()=>{\n        if (confirm(\"确定要删除选中的 \".concat(selectedOrders.length, \" 个订单吗？\"))) {\n            try {\n                // 这里应该调用API删除，现在先本地删除\n                setOrders((prevOrders)=>prevOrders.filter((order)=>!selectedOrders.includes(order.id)));\n                setSelectedOrders([]);\n            } catch (error) {\n                console.error(\"Failed to delete orders:\", error);\n            }\n        }\n    };\n    // 获取过滤和排序后的订单\n    const getFilteredAndSortedOrders = ()=>{\n        let filtered = orders;\n        // 状态过滤\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((order)=>order.order_status === statusFilter);\n        }\n        // 价格过滤\n        if (priceFilter !== \"all\") {\n            filtered = filtered.filter((order)=>order.price === priceFilter);\n        }\n        // 排序\n        filtered.sort((a, b)=>{\n            if (sortBy === \"created_at\") {\n                const dateA = new Date(a.created_at).getTime();\n                const dateB = new Date(b.created_at).getTime();\n                return sortOrder === \"asc\" ? dateA - dateB : dateB - dateA;\n            } else if (sortBy === \"price\") {\n                return sortOrder === \"asc\" ? a.price - b.price : b.price - a.price;\n            }\n            return 0;\n        });\n        return filtered;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this);\n    }\n    // 拖拽功能\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        const rect = e.currentTarget.getBoundingClientRect();\n        setDragOffset({\n            x: e.clientX - rect.left,\n            y: e.clientY - rect.top\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            if (isDragging) {\n                setBatchPanelPosition({\n                    x: e.clientX - dragOffset.x,\n                    y: e.clientY - dragOffset.y\n                });\n            }\n        };\n        const handleMouseUp = ()=>{\n            setIsDragging(false);\n        };\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        dragOffset\n    ]);\n    // 搜索功能\n    const handleSearch = ()=>{\n        if (!searchQuery.trim()) {\n            setSearchResult(null);\n            return;\n        }\n        const result = orders.find((order)=>order.order_id.toLowerCase().includes(searchQuery.toLowerCase()) || order.customer_contact.toLowerCase().includes(searchQuery.toLowerCase()));\n        setSearchResult(result || \"not_found\");\n    };\n    // 渲染内容\n    const renderContent = ()=>{\n        switch(activeTab){\n            case \"orders\":\n                return renderOrdersContent();\n            case \"statistics\":\n                return renderStatisticsContent();\n            case \"search\":\n                return renderSearchContent();\n            case \"trash\":\n                return renderTrashContent();\n            default:\n                return renderOrdersContent();\n        }\n    };\n    const renderSearchContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDD0D 订单查询\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"输入订单ID或客户邮箱...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                onKeyPress: (e)=>e.key === \"Enter\" && handleSearch(),\n                                className: \"flex-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleSearch,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"搜索\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: searchResult === \"not_found\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"未找到匹配的订单\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border rounded-lg p-4 bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"找到订单：\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"订单ID:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.order_id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"客户:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.customer_contact\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"服务:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.service_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"价格:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" $\",\n                                                searchResult.price\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"状态:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.order_status\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"创建时间:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(searchResult.created_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    className: \"mt-4\",\n                                    onClick: ()=>setSelectedOrder(searchResult),\n                                    children: \"查看详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 321,\n            columnNumber: 5\n        }, this);\n    const renderTrashContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDDD1️ 回收站\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"回收站功能开发中...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 371,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 386,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50 w-80 bg-gradient-to-br from-blue-600 to-blue-800 text-white transform transition-transform duration-300 ease-in-out lg:translate-x-0 \".concat(sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"插画工作室\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: \"管理系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden text-white hover:bg-white hover:bg-opacity-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: menuItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setActiveTab(item.id);\n                                            setSidebarOpen(false);\n                                        },\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors \".concat(activeTab === item.id ? \"bg-white bg-opacity-20 text-white\" : \"text-blue-100 hover:bg-white hover:bg-opacity-10 hover:text-white\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(activeTab === item.id ? \"bg-white bg-opacity-20\" : \"bg-gray-100\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs \".concat(activeTab === item.id ? \"text-blue-100\" : \"text-gray-500\"),\n                                                        children: [\n                                                            item.id === \"orders\" && \"管理所有订单\",\n                                                            item.id === \"statistics\" && \"数据分析统计\",\n                                                            item.id === \"search\" && \"快速查询订单\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 p-6 border-t bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"插画工作室管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-1\",\n                                        children: \"v1.0.0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:ml-80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b px-6 py-4 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSidebarOpen(true),\n                                        className: \"lg:hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: (_menuItems_find = menuItems.find((item)=>item.id === activeTab)) === null || _menuItems_find === void 0 ? void 0 : _menuItems_find.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    activeTab === \"orders\" && \"共 \".concat(orders.length, \" 个订单\"),\n                                                    activeTab === \"statistics\" && \"业务数据分析\",\n                                                    activeTab === \"search\" && \"快速查询功能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchOrders,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"刷新\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: renderContent()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n        lineNumber: 383,\n        columnNumber: 5\n    }, this);\n    // 订单管理内容\n    function renderOrdersContent() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                selectedOrders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed z-50 bg-white border-2 border-blue-300 rounded-xl shadow-2xl min-w-80 max-w-96\",\n                    style: {\n                        left: \"\".concat(batchPanelPosition.x, \"px\"),\n                        top: \"\".concat(batchPanelPosition.y, \"px\"),\n                        cursor: isDragging ? \"grabbing\" : \"grab\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 text-white px-4 py-2 rounded-t-xl cursor-grab active:cursor-grabbing flex items-center justify-between\",\n                            onMouseDown: handleMouseDown,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: [\n                                            \"\\uD83D\\uDCCB 已选择 \",\n                                            selectedOrders.length,\n                                            \" 个订单 - 修改为\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>setSelectedOrders([]),\n                                    className: \"text-white hover:bg-blue-600 h-6 w-6 p-0\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"pending\"),\n                                            className: \"bg-gray-500 hover:bg-gray-600 text-white w-full\",\n                                            children: \"⏳ 待处理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"in_progress\"),\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white w-full\",\n                                            children: \"\\uD83D\\uDD04 制作中\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"completed\"),\n                                            className: \"bg-green-500 hover:bg-green-600 text-white w-full\",\n                                            children: \"✅ 已完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            variant: \"destructive\",\n                                            onClick: handleBatchDelete,\n                                            className: \"w-full\",\n                                            children: \"\\uD83D\\uDDD1️ 删除\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-600 mb-3 font-medium\",\n                                            children: \"⏰ 剩余时间调整\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.5\",\n                                                            placeholder: \"输入小时数\",\n                                                            value: timeAdjustmentInput,\n                                                            onChange: (e)=>setTimeAdjustmentInput(e.target.value),\n                                                            className: \"flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            onClick: handleBatchTimeAdjustment,\n                                                            className: \"bg-blue-500 hover:bg-blue-600 text-white text-xs h-7 px-3\",\n                                                            disabled: !timeAdjustmentInput.trim(),\n                                                            children: \"调整\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 正数增加剩余时间，负数减少剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 例如：输入 2 增加2小时剩余时间，输入 -1.5 减少1.5小时剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 直接修改订单的实际剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-4 rounded-lg shadow-sm border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"状态:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pending\",\n                                                children: \"待处理\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"in_progress\",\n                                                children: \"制作中\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"价格:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: priceFilter,\n                                        onChange: (e)=>setPriceFilter(e.target.value === \"all\" ? \"all\" : Number(e.target.value)),\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 5,\n                                                children: \"$5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 7,\n                                                children: \"$7\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 10,\n                                                children: \"$10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"排序:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: \"\".concat(sortBy, \"-\").concat(sortOrder),\n                                        onChange: (e)=>{\n                                            const [field, order] = e.target.value.split(\"-\");\n                                            setSortBy(field);\n                                            setSortOrder(order);\n                                        },\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"created_at-desc\",\n                                                children: \"最新订单\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"created_at-asc\",\n                                                children: \"最早订单\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"price-desc\",\n                                                children: \"价格高到低\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"price-asc\",\n                                                children: \"价格低到高\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-auto text-sm text-gray-500\",\n                                children: [\n                                    \"显示 \",\n                                    getFilteredAndSortedOrders().length,\n                                    \" / \",\n                                    orders.length,\n                                    \" 个订单\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-12 gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedOrders.length === getFilteredAndSortedOrders().length && getFilteredAndSortedOrders().length > 0,\n                                            onChange: handleSelectAll,\n                                            className: \"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"订单ID\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"剩余时间\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"状态\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"服务类型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"操作\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y\",\n                            children: getFilteredAndSortedOrders().map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 hover:bg-gray-50 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-12 gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedOrders.includes(order.id),\n                                                    onChange: ()=>handleSelectOrder(order.id),\n                                                    className: \"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded\",\n                                                    children: order.order_id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm px-3 py-1 rounded-full whitespace-nowrap \".concat(getTimeStatus(order) === \"overdue\" ? \"bg-red-100 text-red-700\" : getTimeStatus(order) === \"warning\" ? \"bg-yellow-100 text-yellow-700\" : getTimeStatus(order) === \"completed\" ? \"bg-green-100 text-green-700\" : \"bg-blue-100 text-blue-700\"),\n                                                    children: order.order_status === \"completed\" ? \"用时\".concat(Math.round(getRemainingTime(order).totalHours), \"小时\") : \"剩余\".concat(formatTimeRemaining(getRemainingTime(order).remainingMs))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    className: \"w-16 text-center \".concat(order.order_status === \"completed\" ? \"bg-green-100 text-green-800\" : order.order_status === \"in_progress\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: order.order_status === \"completed\" ? \"已完成\" : order.order_status === \"in_progress\" ? \"制作中\" : \"待处理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: order.service_type === \"brand\" ? \"\\uD83C\\uDFA8\" : order.service_type === \"character\" ? \"\\uD83D\\uDC64\" : \"\\uD83D\\uDDBC️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: order.service_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-bold\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        order.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setSelectedOrder(order),\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: \"\\uD83D\\uDC41️\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 17\n                                    }, this)\n                                }, order.id, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 11\n                        }, this),\n                        getFilteredAndSortedOrders().length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"没有找到匹配的订单\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 509,\n            columnNumber: 7\n        }, this);\n    }\n    // 数据统计内容 - 简化版本\n    function renderStatisticsContent() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDCCA 数据统计\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 811,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: orders.length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"总订单数\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: orders.filter((o)=>o.order_status === \"completed\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"已完成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 817,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-yellow-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-600\",\n                                        children: orders.filter((o)=>o.order_status === \"in_progress\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"制作中\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 823,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-purple-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: [\n                                            \"$\",\n                                            orders.filter((o)=>o.payment_status === \"completed\").reduce((sum, o)=>sum + o.price, 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"总收入\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 829,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 810,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 809,\n            columnNumber: 7\n        }, this);\n    }\n}\n_s(AdminDashboard, \"MmhEBZrjJuYe4ZmNPhGEl64wFKY=\");\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});