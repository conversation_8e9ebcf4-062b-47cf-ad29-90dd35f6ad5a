"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminDashboard() {\n    var _menuItems_find;\n    _s();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"orders\");\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchOrders = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/orders\");\n            const data = await response.json();\n            if (data.success) {\n                setOrders(data.data.orders);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch orders:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchOrders();\n    }, []);\n    const menuItems = [\n        {\n            id: \"orders\",\n            label: \"订单\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: \"statistics\",\n            label: \"数据统计\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"search\",\n            label: \"查询\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: \"trash\",\n            label: \"回收站\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    // 批量选择功能\n    const handleSelectOrder = (orderId)=>{\n        setSelectedOrders((prev)=>{\n            if (prev.includes(orderId)) {\n                return prev.filter((id)=>id !== orderId);\n            } else {\n                return [\n                    ...prev,\n                    orderId\n                ];\n            }\n        });\n    };\n    // 批量调整时间 - 记录调整量\n    const handleBatchTimeAdjustment = ()=>{\n        const hours = parseFloat(timeAdjustmentInput);\n        if (isNaN(hours) || hours === 0) {\n            alert(\"请输入有效的时间数值（可以是负数）\");\n            return;\n        }\n        // 记录每个选中订单的时间调整\n        setTimeAdjustments((prev)=>{\n            const newAdjustments = {\n                ...prev\n            };\n            selectedOrders.forEach((orderId)=>{\n                const currentAdjustment = newAdjustments[orderId] || 0;\n                newAdjustments[orderId] = currentAdjustment + hours;\n            });\n            return newAdjustments;\n        });\n        // 清空输入框\n        setTimeAdjustmentInput(\"\");\n    };\n    // 全选/取消全选\n    const handleSelectAll = ()=>{\n        const filteredOrders = getFilteredAndSortedOrders();\n        if (selectedOrders.length === filteredOrders.length && filteredOrders.length > 0) {\n            setSelectedOrders([]);\n        } else {\n            setSelectedOrders(filteredOrders.map((order)=>order.id));\n        }\n    };\n    // 批量状态更新\n    const handleBatchStatusChange = async (newStatus)=>{\n        try {\n            // 这里应该调用API更新状态，现在先本地更新\n            setOrders((prevOrders)=>prevOrders.map((order)=>selectedOrders.includes(order.id) ? {\n                        ...order,\n                        order_status: newStatus,\n                        status_updated_at: new Date().toISOString()\n                    } : order));\n            setSelectedOrders([]);\n        } catch (error) {\n            console.error(\"Failed to update order status:\", error);\n        }\n    };\n    // 批量删除\n    const handleBatchDelete = async ()=>{\n        if (confirm(\"确定要删除选中的 \".concat(selectedOrders.length, \" 个订单吗？\"))) {\n            try {\n                // 这里应该调用API删除，现在先本地删除\n                setOrders((prevOrders)=>prevOrders.filter((order)=>!selectedOrders.includes(order.id)));\n                setSelectedOrders([]);\n            } catch (error) {\n                console.error(\"Failed to delete orders:\", error);\n            }\n        }\n    };\n    // 获取过滤和排序后的订单\n    const getFilteredAndSortedOrders = ()=>{\n        let filtered = orders;\n        // 状态过滤\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((order)=>order.order_status === statusFilter);\n        }\n        // 价格过滤\n        if (priceFilter !== \"all\") {\n            filtered = filtered.filter((order)=>order.price === priceFilter);\n        }\n        // 排序\n        filtered.sort((a, b)=>{\n            if (sortBy === \"created_at\") {\n                const dateA = new Date(a.created_at).getTime();\n                const dateB = new Date(b.created_at).getTime();\n                return sortOrder === \"asc\" ? dateA - dateB : dateB - dateA;\n            } else if (sortBy === \"price\") {\n                return sortOrder === \"asc\" ? a.price - b.price : b.price - a.price;\n            }\n            return 0;\n        });\n        return filtered;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    }\n    // 拖拽功能\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        const rect = e.currentTarget.getBoundingClientRect();\n        setDragOffset({\n            x: e.clientX - rect.left,\n            y: e.clientY - rect.top\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            if (isDragging) {\n                setBatchPanelPosition({\n                    x: e.clientX - dragOffset.x,\n                    y: e.clientY - dragOffset.y\n                });\n            }\n        };\n        const handleMouseUp = ()=>{\n            setIsDragging(false);\n        };\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        dragOffset\n    ]);\n    // 搜索功能\n    const handleSearch = ()=>{\n        if (!searchQuery.trim()) {\n            setSearchResult(null);\n            return;\n        }\n        const result = orders.find((order)=>order.order_id.toLowerCase().includes(searchQuery.toLowerCase()) || order.customer_contact.toLowerCase().includes(searchQuery.toLowerCase()));\n        setSearchResult(result || \"not_found\");\n    };\n    // 渲染内容\n    const renderContent = ()=>{\n        switch(activeTab){\n            case \"orders\":\n                return renderOrdersContent();\n            case \"statistics\":\n                return renderStatisticsContent();\n            case \"search\":\n                return renderSearchContent();\n            case \"trash\":\n                return renderTrashContent();\n            default:\n                return renderOrdersContent();\n        }\n    };\n    const renderSearchContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDD0D 订单查询\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"输入订单ID或客户邮箱...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                onKeyPress: (e)=>e.key === \"Enter\" && handleSearch(),\n                                className: \"flex-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleSearch,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"搜索\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: searchResult === \"not_found\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"未找到匹配的订单\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border rounded-lg p-4 bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"找到订单：\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"订单ID:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.order_id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"客户:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.customer_contact\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"服务:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.service_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"价格:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" $\",\n                                                searchResult.price\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"状态:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.order_status\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"创建时间:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(searchResult.created_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    className: \"mt-4\",\n                                    onClick: ()=>setSelectedOrder(searchResult),\n                                    children: \"查看详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 225,\n            columnNumber: 5\n        }, this);\n    const renderTrashContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDDD1️ 回收站\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"回收站功能开发中...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 275,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50 w-80 bg-gradient-to-br from-blue-600 to-blue-800 text-white transform transition-transform duration-300 ease-in-out lg:translate-x-0 \".concat(sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"插画工作室\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: \"管理系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden text-white hover:bg-white hover:bg-opacity-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: menuItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setActiveTab(item.id);\n                                            setSidebarOpen(false);\n                                        },\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors \".concat(activeTab === item.id ? \"bg-white bg-opacity-20 text-white\" : \"text-blue-100 hover:bg-white hover:bg-opacity-10 hover:text-white\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(activeTab === item.id ? \"bg-white bg-opacity-20\" : \"bg-gray-100\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs \".concat(activeTab === item.id ? \"text-blue-100\" : \"text-gray-500\"),\n                                                        children: [\n                                                            item.id === \"orders\" && \"管理所有订单\",\n                                                            item.id === \"statistics\" && \"数据分析统计\",\n                                                            item.id === \"search\" && \"快速查询订单\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 p-6 border-t bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"插画工作室管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-1\",\n                                        children: \"v1.0.0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:ml-80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b px-6 py-4 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSidebarOpen(true),\n                                        className: \"lg:hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: (_menuItems_find = menuItems.find((item)=>item.id === activeTab)) === null || _menuItems_find === void 0 ? void 0 : _menuItems_find.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    activeTab === \"orders\" && \"共 \".concat(orders.length, \" 个订单\"),\n                                                    activeTab === \"statistics\" && \"业务数据分析\",\n                                                    activeTab === \"search\" && \"快速查询功能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchOrders,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"刷新\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: renderContent()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, this);\n    // 订单管理内容\n    function renderOrdersContent() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                selectedOrders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed z-50 bg-white border-2 border-blue-300 rounded-xl shadow-2xl min-w-80 max-w-96\",\n                    style: {\n                        left: \"\".concat(batchPanelPosition.x, \"px\"),\n                        top: \"\".concat(batchPanelPosition.y, \"px\"),\n                        cursor: isDragging ? \"grabbing\" : \"grab\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 text-white px-4 py-2 rounded-t-xl cursor-grab active:cursor-grabbing flex items-center justify-between\",\n                            onMouseDown: handleMouseDown,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: [\n                                            \"\\uD83D\\uDCCB 已选择 \",\n                                            selectedOrders.length,\n                                            \" 个订单 - 修改为\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>setSelectedOrders([]),\n                                    className: \"text-white hover:bg-blue-600 h-6 w-6 p-0\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"pending\"),\n                                            className: \"bg-gray-500 hover:bg-gray-600 text-white w-full\",\n                                            children: \"⏳ 待处理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"in_progress\"),\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white w-full\",\n                                            children: \"\\uD83D\\uDD04 制作中\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"completed\"),\n                                            className: \"bg-green-500 hover:bg-green-600 text-white w-full\",\n                                            children: \"✅ 已完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            variant: \"destructive\",\n                                            onClick: handleBatchDelete,\n                                            className: \"w-full\",\n                                            children: \"\\uD83D\\uDDD1️ 删除\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-600 mb-3 font-medium\",\n                                            children: \"⏰ 剩余时间调整\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.5\",\n                                                            placeholder: \"输入小时数\",\n                                                            value: timeAdjustmentInput,\n                                                            onChange: (e)=>setTimeAdjustmentInput(e.target.value),\n                                                            className: \"flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            onClick: handleBatchTimeAdjustment,\n                                                            className: \"bg-blue-500 hover:bg-blue-600 text-white text-xs h-7 px-3\",\n                                                            disabled: !timeAdjustmentInput.trim(),\n                                                            children: \"调整\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 正数增加剩余时间，负数减少剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 例如：输入 2 增加2小时剩余时间，输入 -1.5 减少1.5小时剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 直接修改订单的实际剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-4 rounded-lg shadow-sm border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"状态:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pending\",\n                                                children: \"待处理\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"in_progress\",\n                                                children: \"制作中\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"价格:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: priceFilter,\n                                        onChange: (e)=>setPriceFilter(e.target.value === \"all\" ? \"all\" : Number(e.target.value)),\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 5,\n                                                children: \"$5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 7,\n                                                children: \"$7\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 10,\n                                                children: \"$10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"排序:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: \"\".concat(sortBy, \"-\").concat(sortOrder),\n                                        onChange: (e)=>{\n                                            const [field, order] = e.target.value.split(\"-\");\n                                            setSortBy(field);\n                                            setSortOrder(order);\n                                        },\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"created_at-desc\",\n                                                children: \"最新订单\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"created_at-asc\",\n                                                children: \"最早订单\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"price-desc\",\n                                                children: \"价格高到低\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"price-asc\",\n                                                children: \"价格低到高\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-auto text-sm text-gray-500\",\n                                children: [\n                                    \"显示 \",\n                                    getFilteredAndSortedOrders().length,\n                                    \" / \",\n                                    orders.length,\n                                    \" 个订单\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-12 gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedOrders.length === getFilteredAndSortedOrders().length && getFilteredAndSortedOrders().length > 0,\n                                            onChange: handleSelectAll,\n                                            className: \"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"订单ID\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"剩余时间\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"状态\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"服务类型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"操作\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y\",\n                            children: getFilteredAndSortedOrders().map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 hover:bg-gray-50 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-12 gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedOrders.includes(order.id),\n                                                    onChange: ()=>handleSelectOrder(order.id),\n                                                    className: \"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded\",\n                                                    children: order.order_id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm px-3 py-1 rounded-full whitespace-nowrap \".concat(getTimeStatus(order) === \"overdue\" ? \"bg-red-100 text-red-700\" : getTimeStatus(order) === \"warning\" ? \"bg-yellow-100 text-yellow-700\" : getTimeStatus(order) === \"completed\" ? \"bg-green-100 text-green-700\" : \"bg-blue-100 text-blue-700\"),\n                                                    children: order.order_status === \"completed\" ? \"用时\".concat(Math.round(getRemainingTime(order).totalHours), \"小时\") : \"剩余\".concat(formatTimeRemaining(getRemainingTime(order).remainingMs))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    className: \"w-16 text-center \".concat(order.order_status === \"completed\" ? \"bg-green-100 text-green-800\" : order.order_status === \"in_progress\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: order.order_status === \"completed\" ? \"已完成\" : order.order_status === \"in_progress\" ? \"制作中\" : \"待处理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: order.service_type === \"brand\" ? \"\\uD83C\\uDFA8\" : order.service_type === \"character\" ? \"\\uD83D\\uDC64\" : \"\\uD83D\\uDDBC️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: order.service_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-bold\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        order.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setSelectedOrder(order),\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: \"\\uD83D\\uDC41️\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 17\n                                    }, this)\n                                }, order.id, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this),\n                        getFilteredAndSortedOrders().length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"没有找到匹配的订单\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this);\n    }\n    // 数据统计内容 - 简化版本\n    function renderStatisticsContent() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDCCA 数据统计\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: orders.length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"总订单数\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 717,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: orders.filter((o)=>o.order_status === \"completed\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"已完成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-yellow-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-600\",\n                                        children: orders.filter((o)=>o.order_status === \"in_progress\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"制作中\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 727,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-purple-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: [\n                                            \"$\",\n                                            orders.filter((o)=>o.payment_status === \"completed\").reduce((sum, o)=>sum + o.price, 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"总收入\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 714,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 713,\n            columnNumber: 7\n        }, this);\n    }\n}\n_s(AdminDashboard, \"FsgazoaHtwy53cMdJ9gXt5zw+PU=\");\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});