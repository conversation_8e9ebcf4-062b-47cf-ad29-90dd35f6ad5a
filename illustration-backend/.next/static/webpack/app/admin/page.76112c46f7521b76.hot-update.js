"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Menu,Search,ShoppingCart,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminDashboard() {\n    var _menuItems_find;\n    _s();\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"orders\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResult, setSearchResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [priceFilter, setPriceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"created_at\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desc\");\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [selectedOrders, setSelectedOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [batchPanelPosition, setBatchPanelPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 20,\n        y: 100\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [timeAdjustmentInput, setTimeAdjustmentInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [timeAdjustments, setTimeAdjustments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchOrders = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/orders\");\n            const data = await response.json();\n            if (data.success) {\n                // 为订单添加序号和其他必要字段\n                const ordersWithExtras = data.data.orders.map((order, index)=>({\n                        ...order,\n                        sequence_number: index + 1,\n                        status_updated_at: order.updated_at,\n                        reference_images: order.style_images || [],\n                        completed_images: order.order_status === \"completed\" ? [\n                            \"https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600\"\n                        ] : [],\n                        admin_notes: \"\"\n                    }));\n                setOrders(ordersWithExtras);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch orders:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchOrders();\n    }, []);\n    // 每分钟更新一次时间，保持倒计时准确\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000) // 每分钟更新\n        ;\n        return ()=>clearInterval(timer);\n    }, []);\n    const menuItems = [\n        {\n            id: \"orders\",\n            label: \"订单\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: \"statistics\",\n            label: \"数据统计\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"search\",\n            label: \"查询\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: \"trash\",\n            label: \"回收站\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    // 根据价格确定处理小时数\n    const getProcessingHours = (price)=>{\n        if (price === 5) return 48 // 48小时\n        ;\n        if (price === 7) return 72 // 72小时\n        ;\n        if (price === 10) return 96 // 96小时\n        ;\n        return 48 // 默认\n        ;\n    };\n    // 制作时间固定2小时\n    const PRODUCTION_HOURS = 2;\n    // 计算剩余时间\n    const getRemainingTime = (order)=>{\n        const now = currentTime;\n        const createdAt = new Date(order.created_at);\n        const statusUpdatedAt = new Date(order.status_updated_at);\n        // 获取该订单的时间调整（以小时为单位）\n        const timeAdjustmentHours = timeAdjustments[order.id] || 0;\n        if (order.order_status === \"pending\") {\n            // 待处理：从创建时间开始计算\n            const processingHours = getProcessingHours(order.price);\n            const deadlineTime = new Date(createdAt.getTime() + processingHours * 60 * 60 * 1000);\n            const baseRemainingMs = deadlineTime.getTime() - now.getTime();\n            // 直接在剩余时间上加上调整量\n            const adjustedRemainingMs = baseRemainingMs + timeAdjustmentHours * 60 * 60 * 1000;\n            return {\n                remainingMs: adjustedRemainingMs,\n                totalHours: processingHours,\n                type: \"processing\"\n            };\n        } else if (order.order_status === \"in_progress\") {\n            // 制作中：从状态更新时间开始计算2小时\n            const deadlineTime = new Date(statusUpdatedAt.getTime() + PRODUCTION_HOURS * 60 * 60 * 1000);\n            const baseRemainingMs = deadlineTime.getTime() - now.getTime();\n            // 直接在剩余时间上加上调整量\n            const adjustedRemainingMs = baseRemainingMs + timeAdjustmentHours * 60 * 60 * 1000;\n            return {\n                remainingMs: adjustedRemainingMs,\n                totalHours: PRODUCTION_HOURS,\n                type: \"production\"\n            };\n        } else {\n            // 已完成：计算实际用时\n            const totalMs = statusUpdatedAt.getTime() - createdAt.getTime();\n            return {\n                remainingMs: 0,\n                totalHours: totalMs / (1000 * 60 * 60),\n                type: \"completed\"\n            };\n        }\n    };\n    // 格式化时间显示\n    const formatTimeRemaining = (remainingMs)=>{\n        if (remainingMs <= 0) return \"已超时\";\n        const hours = Math.floor(remainingMs / (1000 * 60 * 60));\n        const minutes = Math.floor(remainingMs % (1000 * 60 * 60) / (1000 * 60));\n        if (hours > 0) {\n            return \"\".concat(hours, \"小时\").concat(minutes, \"分钟\");\n        } else {\n            return \"\".concat(minutes, \"分钟\");\n        }\n    };\n    // 获取时间状态颜色\n    const getTimeStatus = (order)=>{\n        const { remainingMs, totalHours } = getRemainingTime(order);\n        if (order.order_status === \"completed\") {\n            return \"completed\";\n        }\n        if (remainingMs <= 0) {\n            return \"overdue\" // 已超时\n            ;\n        }\n        const totalMs = totalHours * 60 * 60 * 1000;\n        const remainingPercent = remainingMs / totalMs;\n        if (remainingPercent < 0.25) {\n            return \"warning\" // 即将到期\n            ;\n        }\n        return \"normal\" // 正常\n        ;\n    };\n    // 批量选择功能\n    const handleSelectOrder = (orderId)=>{\n        setSelectedOrders((prev)=>{\n            if (prev.includes(orderId)) {\n                return prev.filter((id)=>id !== orderId);\n            } else {\n                return [\n                    ...prev,\n                    orderId\n                ];\n            }\n        });\n    };\n    // 批量调整时间 - 记录调整量\n    const handleBatchTimeAdjustment = ()=>{\n        const hours = parseFloat(timeAdjustmentInput);\n        if (isNaN(hours) || hours === 0) {\n            alert(\"请输入有效的时间数值（可以是负数）\");\n            return;\n        }\n        // 记录每个选中订单的时间调整\n        setTimeAdjustments((prev)=>{\n            const newAdjustments = {\n                ...prev\n            };\n            selectedOrders.forEach((orderId)=>{\n                const currentAdjustment = newAdjustments[orderId] || 0;\n                newAdjustments[orderId] = currentAdjustment + hours;\n            });\n            return newAdjustments;\n        });\n        // 清空输入框\n        setTimeAdjustmentInput(\"\");\n    };\n    // 全选/取消全选\n    const handleSelectAll = ()=>{\n        const filteredOrders = getFilteredAndSortedOrders();\n        if (selectedOrders.length === filteredOrders.length && filteredOrders.length > 0) {\n            setSelectedOrders([]);\n        } else {\n            setSelectedOrders(filteredOrders.map((order)=>order.id));\n        }\n    };\n    // 批量状态更新\n    const handleBatchStatusChange = async (newStatus)=>{\n        try {\n            // 这里应该调用API更新状态，现在先本地更新\n            setOrders((prevOrders)=>prevOrders.map((order)=>selectedOrders.includes(order.id) ? {\n                        ...order,\n                        order_status: newStatus,\n                        status_updated_at: new Date().toISOString()\n                    } : order));\n            setSelectedOrders([]);\n        } catch (error) {\n            console.error(\"Failed to update order status:\", error);\n        }\n    };\n    // 批量删除\n    const handleBatchDelete = async ()=>{\n        if (confirm(\"确定要删除选中的 \".concat(selectedOrders.length, \" 个订单吗？\"))) {\n            try {\n                // 这里应该调用API删除，现在先本地删除\n                setOrders((prevOrders)=>prevOrders.filter((order)=>!selectedOrders.includes(order.id)));\n                setSelectedOrders([]);\n            } catch (error) {\n                console.error(\"Failed to delete orders:\", error);\n            }\n        }\n    };\n    // 获取过滤和排序后的订单\n    const getFilteredAndSortedOrders = ()=>{\n        let filtered = orders;\n        // 状态过滤\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((order)=>order.order_status === statusFilter);\n        }\n        // 价格过滤\n        if (priceFilter !== \"all\") {\n            filtered = filtered.filter((order)=>order.price === priceFilter);\n        }\n        // 排序\n        filtered.sort((a, b)=>{\n            if (sortBy === \"created_at\") {\n                const dateA = new Date(a.created_at).getTime();\n                const dateB = new Date(b.created_at).getTime();\n                return sortOrder === \"asc\" ? dateA - dateB : dateB - dateA;\n            } else if (sortBy === \"price\") {\n                return sortOrder === \"asc\" ? a.price - b.price : b.price - a.price;\n            }\n            return 0;\n        });\n        return filtered;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 261,\n            columnNumber: 7\n        }, this);\n    }\n    // 拖拽功能\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        const rect = e.currentTarget.getBoundingClientRect();\n        setDragOffset({\n            x: e.clientX - rect.left,\n            y: e.clientY - rect.top\n        });\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setBatchPanelPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging\n    ]);\n    // 搜索功能\n    const handleSearch = ()=>{\n        if (!searchQuery.trim()) {\n            setSearchResult(null);\n            return;\n        }\n        const result = orders.find((order)=>order.order_id.toLowerCase().includes(searchQuery.toLowerCase()) || order.customer_contact.toLowerCase().includes(searchQuery.toLowerCase()));\n        setSearchResult(result || \"not_found\");\n    };\n    // 渲染内容\n    const renderContent = ()=>{\n        switch(activeTab){\n            case \"orders\":\n                return renderOrdersContent();\n            case \"statistics\":\n                return renderStatisticsContent();\n            case \"search\":\n                return renderSearchContent();\n            case \"trash\":\n                return renderTrashContent();\n            default:\n                return renderOrdersContent();\n        }\n    };\n    const renderSearchContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDD0D 订单查询\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"输入订单ID或客户邮箱...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                onKeyPress: (e)=>e.key === \"Enter\" && handleSearch(),\n                                className: \"flex-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleSearch,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"搜索\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this),\n                    searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: searchResult === \"not_found\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"未找到匹配的订单\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border rounded-lg p-4 bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"找到订单：\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"订单ID:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.order_id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"客户:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.customer_contact\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"服务:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.service_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"价格:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" $\",\n                                                searchResult.price\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"状态:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                searchResult.order_status\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"创建时间:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" \",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(searchResult.created_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    className: \"mt-4\",\n                                    onClick: ()=>setSelectedOrder(searchResult),\n                                    children: \"查看详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 336,\n            columnNumber: 5\n        }, this);\n    const renderTrashContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDDD1️ 回收站\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"回收站功能开发中...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 386,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 401,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50 w-80 bg-gradient-to-br from-blue-600 to-blue-800 text-white transform transition-transform duration-300 ease-in-out lg:translate-x-0 \".concat(sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"插画工作室\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: \"管理系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden text-white hover:bg-white hover:bg-opacity-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: menuItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setActiveTab(item.id);\n                                            setSidebarOpen(false);\n                                        },\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors \".concat(activeTab === item.id ? \"bg-white bg-opacity-20 text-white\" : \"text-blue-100 hover:bg-white hover:bg-opacity-10 hover:text-white\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(activeTab === item.id ? \"bg-white bg-opacity-20\" : \"bg-gray-100\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs \".concat(activeTab === item.id ? \"text-blue-100\" : \"text-gray-500\"),\n                                                        children: [\n                                                            item.id === \"orders\" && \"管理所有订单\",\n                                                            item.id === \"statistics\" && \"数据分析统计\",\n                                                            item.id === \"search\" && \"快速查询订单\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 p-6 border-t bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"插画工作室管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-1\",\n                                        children: \"v1.0.0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:ml-80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b px-6 py-4 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSidebarOpen(true),\n                                        className: \"lg:hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: (_menuItems_find = menuItems.find((item)=>item.id === activeTab)) === null || _menuItems_find === void 0 ? void 0 : _menuItems_find.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    activeTab === \"orders\" && \"共 \".concat(orders.length, \" 个订单\"),\n                                                    activeTab === \"statistics\" && \"业务数据分析\",\n                                                    activeTab === \"search\" && \"快速查询功能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchOrders,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"刷新\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: renderContent()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n        lineNumber: 398,\n        columnNumber: 5\n    }, this);\n    // 订单管理内容\n    function renderOrdersContent() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                selectedOrders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed z-50 bg-white border-2 border-blue-300 rounded-xl shadow-2xl min-w-80 max-w-96\",\n                    style: {\n                        left: \"\".concat(batchPanelPosition.x, \"px\"),\n                        top: \"\".concat(batchPanelPosition.y, \"px\"),\n                        cursor: isDragging ? \"grabbing\" : \"grab\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 text-white px-4 py-2 rounded-t-xl cursor-grab active:cursor-grabbing flex items-center justify-between\",\n                            onMouseDown: handleMouseDown,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: [\n                                            \"\\uD83D\\uDCCB 已选择 \",\n                                            selectedOrders.length,\n                                            \" 个订单 - 修改为\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    onClick: ()=>setSelectedOrders([]),\n                                    className: \"text-white hover:bg-blue-600 h-6 w-6 p-0\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"pending\"),\n                                            className: \"bg-gray-500 hover:bg-gray-600 text-white w-full\",\n                                            children: \"⏳ 待处理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"in_progress\"),\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white w-full\",\n                                            children: \"\\uD83D\\uDD04 制作中\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>handleBatchStatusChange(\"completed\"),\n                                            className: \"bg-green-500 hover:bg-green-600 text-white w-full\",\n                                            children: \"✅ 已完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            variant: \"destructive\",\n                                            onClick: handleBatchDelete,\n                                            className: \"w-full\",\n                                            children: \"\\uD83D\\uDDD1️ 删除\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-600 mb-3 font-medium\",\n                                            children: \"⏰ 剩余时间调整\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.5\",\n                                                            placeholder: \"输入小时数\",\n                                                            value: timeAdjustmentInput,\n                                                            onChange: (e)=>setTimeAdjustmentInput(e.target.value),\n                                                            className: \"flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            onClick: handleBatchTimeAdjustment,\n                                                            className: \"bg-blue-500 hover:bg-blue-600 text-white text-xs h-7 px-3\",\n                                                            disabled: !timeAdjustmentInput.trim(),\n                                                            children: \"调整\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 正数增加剩余时间，负数减少剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 例如：输入 2 增加2小时剩余时间，输入 -1.5 减少1.5小时剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"• 直接修改订单的实际剩余时间\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-4 rounded-lg shadow-sm border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"状态:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pending\",\n                                                children: \"待处理\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"in_progress\",\n                                                children: \"制作中\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: \"已完成\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"价格:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: priceFilter,\n                                        onChange: (e)=>setPriceFilter(e.target.value === \"all\" ? \"all\" : Number(e.target.value)),\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 5,\n                                                children: \"$5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 7,\n                                                children: \"$7\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: 10,\n                                                children: \"$10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"排序:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: \"\".concat(sortBy, \"-\").concat(sortOrder),\n                                        onChange: (e)=>{\n                                            const [field, order] = e.target.value.split(\"-\");\n                                            setSortBy(field);\n                                            setSortOrder(order);\n                                        },\n                                        className: \"px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"created_at-desc\",\n                                                children: \"最新订单\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"created_at-asc\",\n                                                children: \"最早订单\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"price-desc\",\n                                                children: \"价格高到低\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"price-asc\",\n                                                children: \"价格低到高\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-auto text-sm text-gray-500\",\n                                children: [\n                                    \"显示 \",\n                                    getFilteredAndSortedOrders().length,\n                                    \" / \",\n                                    orders.length,\n                                    \" 个订单\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 624,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-12 gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedOrders.length === getFilteredAndSortedOrders().length && getFilteredAndSortedOrders().length > 0,\n                                            onChange: handleSelectAll,\n                                            className: \"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"订单ID\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"剩余时间\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"状态\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-2 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"服务类型\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"操作\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y\",\n                            children: getFilteredAndSortedOrders().map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 hover:bg-gray-50 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-12 gap-4 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedOrders.includes(order.id),\n                                                    onChange: ()=>handleSelectOrder(order.id),\n                                                    className: \"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded\",\n                                                    children: order.order_id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-3 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm px-3 py-1 rounded-full whitespace-nowrap \".concat(getTimeStatus(order) === \"overdue\" ? \"bg-red-100 text-red-700\" : getTimeStatus(order) === \"warning\" ? \"bg-yellow-100 text-yellow-700\" : getTimeStatus(order) === \"completed\" ? \"bg-green-100 text-green-700\" : \"bg-blue-100 text-blue-700\"),\n                                                    children: order.order_status === \"completed\" ? \"用时\".concat(Math.round(getRemainingTime(order).totalHours), \"小时\") : \"剩余\".concat(formatTimeRemaining(getRemainingTime(order).remainingMs))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    className: \"w-16 text-center \".concat(order.order_status === \"completed\" ? \"bg-green-100 text-green-800\" : order.order_status === \"in_progress\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: order.order_status === \"completed\" ? \"已完成\" : order.order_status === \"in_progress\" ? \"制作中\" : \"待处理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: order.service_type === \"brand\" ? \"\\uD83C\\uDFA8\" : order.service_type === \"character\" ? \"\\uD83D\\uDC64\" : \"\\uD83D\\uDDBC️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: order.service_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-bold\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        order.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-1 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setSelectedOrder(order),\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: \"\\uD83D\\uDC41️\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, this)\n                                }, order.id, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 11\n                        }, this),\n                        getFilteredAndSortedOrders().length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Menu_Search_ShoppingCart_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"没有找到匹配的订单\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 524,\n            columnNumber: 7\n        }, this);\n    }\n    // 数据统计内容 - 简化版本\n    function renderStatisticsContent() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83D\\uDCCA 数据统计\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: orders.length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"总订单数\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 828,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: orders.filter((o)=>o.order_status === \"completed\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"已完成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-yellow-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-600\",\n                                        children: orders.filter((o)=>o.order_status === \"in_progress\").length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"制作中\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-purple-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: [\n                                            \"$\",\n                                            orders.filter((o)=>o.payment_status === \"completed\").reduce((sum, o)=>sum + o.price, 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"总收入\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n                lineNumber: 825,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/admin/page.tsx\",\n            lineNumber: 824,\n            columnNumber: 7\n        }, this);\n    }\n}\n_s(AdminDashboard, \"KZN5bTVudnTLWhj09OA1VPIBigw=\");\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});