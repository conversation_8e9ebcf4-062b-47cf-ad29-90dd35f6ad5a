{"c": ["app/admin/page", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs", "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx&server=false!", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js", "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js", "(app-pages-browser)/./src/app/admin/page.tsx", "(app-pages-browser)/./src/components/ui/badge.tsx", "(app-pages-browser)/./src/components/ui/button.tsx", "(app-pages-browser)/./src/components/ui/input.tsx", "(app-pages-browser)/./src/lib/utils.ts"]}