"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(middleware)/./node_modules/next/dist/esm/server/web/exports/next-response.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// 简化的token验证函数，支持演示模式\nfunction verifyTokenSimple(token) {\n    try {\n        const JWT_SECRET = process.env.JWT_SECRET || \"demo_jwt_secret_key_for_development_only\";\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return true // 如果能解码就认为有效\n        ;\n    } catch (error) {\n        return false;\n    }\n}\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // 只对 /admin 路径进行保护，但排除登录页面\n    if (pathname.startsWith(\"/admin\") && pathname !== \"/admin/login\") {\n        const token = request.cookies.get(\"admin-token\")?.value;\n        if (!token) {\n            // 没有token，重定向到登录页\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].redirect(new URL(\"/admin/login\", request.url));\n        }\n        // 使用简化的验证方法\n        if (!verifyTokenSimple(token)) {\n            // token无效，重定向到登录页\n            const response = next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].redirect(new URL(\"/admin/login\", request.url));\n            response.cookies.delete(\"admin-token\");\n            return response;\n        }\n        // token有效，继续请求\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].next();\n    }\n    // 如果已经登录，访问登录页面时重定向到管理后台\n    if (pathname === \"/admin/login\") {\n        const token = request.cookies.get(\"admin-token\")?.value;\n        if (token && verifyTokenSimple(token)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].redirect(new URL(\"/admin\", request.url));\n        }\n    }\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].next();\n}\nconst config = {\n    matcher: [\n        \"/admin/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});