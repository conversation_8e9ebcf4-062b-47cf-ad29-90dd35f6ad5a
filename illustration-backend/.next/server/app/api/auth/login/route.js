/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_wa_Desktop_wang_zhan_illustration_backend_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.ts */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/api/auth/login/route.ts\",\n    nextConfigOutput,\n    userland: _Users_wa_Desktop_wang_zhan_illustration_backend_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { email, password } = body;\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Email and password are required\"\n            }, {\n                status: 400\n            });\n        }\n        // 演示模式：允许使用演示账户登录\n        if (email === \"<EMAIL>\" && password === \"admin123\") {\n            // 生成演示用的JWT token\n            const JWT_SECRET = process.env.JWT_SECRET || \"demo_jwt_secret_key_for_development_only\";\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default().sign({\n                userId: \"demo-admin-id\",\n                email: \"<EMAIL>\",\n                role: \"admin\"\n            }, JWT_SECRET, {\n                expiresIn: \"24h\"\n            });\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                user: {\n                    id: \"demo-admin-id\",\n                    email: \"<EMAIL>\",\n                    name: \"Demo Administrator\",\n                    role: \"admin\"\n                }\n            });\n            response.cookies.set(\"admin-token\", token, {\n                httpOnly: true,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\",\n                maxAge: 24 * 60 * 60 // 24 hours\n            });\n            return response;\n        }\n        // 尝试真实的数据库认证\n        const result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateAdmin)({\n            email,\n            password\n        });\n        if (!result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: result.error\n            }, {\n                status: 401\n            });\n        }\n        // 设置HTTP-only cookie\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: result.user\n        });\n        response.cookies.set(\"admin-token\", result.token, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            maxAge: 24 * 60 * 60 // 24 hours\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Login API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateAdmin: () => (/* binding */ authenticateAdmin),\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\n// 验证管理员登录\nasync function authenticateAdmin(credentials) {\n    try {\n        const { email, password } = credentials;\n        // 从数据库获取管理员用户\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(_supabase__WEBPACK_IMPORTED_MODULE_2__.TABLES.ADMIN_USERS).select(\"*\").eq(\"email\", email).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                success: false,\n                error: \"Invalid email or password\"\n            };\n        }\n        // 验证密码\n        const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: \"Invalid email or password\"\n            };\n        }\n        // 更新最后登录时间\n        await _supabase__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(_supabase__WEBPACK_IMPORTED_MODULE_2__.TABLES.ADMIN_USERS).update({\n            last_login: new Date().toISOString()\n        }).eq(\"id\", user.id);\n        // 生成JWT token\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, JWT_SECRET, {\n            expiresIn: \"24h\"\n        });\n        return {\n            success: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                name: user.name,\n                role: user.role\n            },\n            token\n        };\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        return {\n            success: false,\n            error: \"Authentication failed\"\n        };\n    }\n}\n// 验证JWT token\nasync function verifyToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        // 从数据库验证用户是否仍然有效\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(_supabase__WEBPACK_IMPORTED_MODULE_2__.TABLES.ADMIN_USERS).select(\"id, email, name, role\").eq(\"id\", decoded.userId).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                success: false,\n                error: \"Invalid token\"\n            };\n        }\n        return {\n            success: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                name: user.name,\n                role: user.role\n            }\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: \"Invalid or expired token\"\n        };\n    }\n}\n// 创建管理员用户\nasync function createAdminUser(email, password, name) {\n    try {\n        // 检查邮箱是否已存在\n        const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(_supabase__WEBPACK_IMPORTED_MODULE_2__.TABLES.ADMIN_USERS).select(\"id\").eq(\"email\", email).single();\n        if (existingUser) {\n            return {\n                success: false,\n                error: \"Email already exists\"\n            };\n        }\n        // 加密密码\n        const passwordHash = await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, 10);\n        // 创建用户\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from(_supabase__WEBPACK_IMPORTED_MODULE_2__.TABLES.ADMIN_USERS).insert({\n            email,\n            password_hash: passwordHash,\n            name,\n            role: \"admin\"\n        }).select(\"id, email, name, role\").single();\n        if (error) {\n            console.error(\"Create user error:\", error);\n            return {\n                success: false,\n                error: \"Failed to create user\"\n            };\n        }\n        return {\n            success: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                name: user.name,\n                role: user.role\n            }\n        };\n    } catch (error) {\n        console.error(\"Create admin user error:\", error);\n        return {\n            success: false,\n            error: \"Failed to create admin user\"\n        };\n    }\n}\n// 中间件：验证管理员权限\nfunction requireAuth() {\n    return async (request)=>{\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return {\n                success: false,\n                error: \"No token provided\"\n            };\n        }\n        const token = authHeader.substring(7);\n        return await verifyToken(token);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_BUCKETS: () => (/* binding */ STORAGE_BUCKETS),\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://demo.supabase.co\";\nconst supabaseAnonKey = \"demo_anon_key\";\nconst supabaseServiceKey = \"demo_service_key\";\n// 客户端实例（用于前端操作）\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// 服务端实例（用于管理员操作，拥有更高权限）\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// 数据库表名\nconst TABLES = {\n    ORDERS: \"orders\",\n    ADMIN_USERS: \"admin_users\"\n};\n// 存储桶名\nconst STORAGE_BUCKETS = {\n    PHOTOS: \"customer-photos\",\n    STYLE_IMAGES: \"style-references\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/semver","vendor-chunks/ws","vendor-chunks/jsonwebtoken","vendor-chunks/whatwg-url","vendor-chunks/jws","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/webidl-conversions","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();