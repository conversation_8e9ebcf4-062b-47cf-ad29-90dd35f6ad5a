/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/orders/route";
exports.ids = ["app/api/orders/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_wa_Desktop_wang_zhan_illustration_backend_src_app_api_orders_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/api/orders/route.ts */ \"(rsc)/./src/app/api/orders/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/orders/route\",\n        pathname: \"/api/orders\",\n        filename: \"route\",\n        bundlePath: \"app/api/orders/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/wang zhan/illustration-backend/src/app/api/orders/route.ts\",\n    nextConfigOutput,\n    userland: _Users_wa_Desktop_wang_zhan_illustration_backend_src_app_api_orders_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/orders/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/orders/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/orders/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n// 获取订单列表\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const status = searchParams.get(\"status\");\n        const serviceType = searchParams.get(\"service_type\");\n        // 演示模式：返回模拟数据\n        const demoOrders = [\n            {\n                id: \"demo-1\",\n                order_id: \"ORD-20241201-ABC123\",\n                service_type: \"brand\",\n                service_name: \"品牌插画\",\n                price: 5,\n                customer_contact: \"<EMAIL>\",\n                requirements: \"需要一个现代简约风格的品牌插画，主要用于网站首页展示。希望能体现科技感和专业性。\",\n                photos: [\n                    \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=300&h=200&fit=crop\",\n                    \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop\"\n                ],\n                style_images: [\n                    \"https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=200&fit=crop\"\n                ],\n                paypal_transaction_id: \"PAYPAL-TXN-123456\",\n                payment_status: \"completed\",\n                order_status: \"in_progress\",\n                created_at: \"2024-12-01T10:30:00Z\",\n                updated_at: \"2024-12-01T14:20:00Z\"\n            },\n            {\n                id: \"demo-2\",\n                order_id: \"ORD-20241201-DEF456\",\n                service_type: \"character\",\n                service_name: \"角色设计\",\n                price: 7,\n                customer_contact: \"<EMAIL>\",\n                requirements: \"设计一个可爱的卡通角色，用于儿童教育应用。希望角色活泼可爱，有亲和力。\",\n                photos: [\n                    \"https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=300&h=200&fit=crop\"\n                ],\n                style_images: [\n                    \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop\",\n                    \"https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=200&fit=crop\"\n                ],\n                paypal_transaction_id: null,\n                payment_status: \"pending\",\n                order_status: \"pending\",\n                created_at: \"2024-12-01T09:15:00Z\",\n                updated_at: \"2024-12-01T09:15:00Z\"\n            },\n            {\n                id: \"demo-3\",\n                order_id: \"ORD-20241130-GHI789\",\n                service_type: \"scene\",\n                service_name: \"场景插画\",\n                price: 10,\n                customer_contact: \"<EMAIL>\",\n                requirements: \"创作一幅梦幻森林场景插画，用于小说封面。需要神秘而美丽的氛围。\",\n                photos: [],\n                style_images: [\n                    \"https://images.unsplash.com/photo-1596548438137-d51ea5c83ca4?w=300&h=200&fit=crop\"\n                ],\n                paypal_transaction_id: \"PAYPAL-TXN-789012\",\n                payment_status: \"completed\",\n                order_status: \"completed\",\n                created_at: \"2024-11-30T16:45:00Z\",\n                updated_at: \"2024-12-01T12:00:00Z\"\n            }\n        ];\n        // 应用过滤条件\n        let filteredOrders = demoOrders;\n        if (status) {\n            filteredOrders = filteredOrders.filter((order)=>order.order_status === status);\n        }\n        if (serviceType) {\n            filteredOrders = filteredOrders.filter((order)=>order.service_type === serviceType);\n        }\n        // 分页\n        const total = filteredOrders.length;\n        const from = (page - 1) * limit;\n        const to = from + limit;\n        const paginatedOrders = filteredOrders.slice(from, to);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: {\n                orders: paginatedOrders,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            }\n        });\n    // 真实数据库查询代码（当配置了 Supabase 时使用）\n    /*\n    let query = supabaseAdmin\n      .from(TABLES.ORDERS)\n      .select('*', { count: 'exact' })\n      .order('created_at', { ascending: false })\n\n    // 添加过滤条件\n    if (status) {\n      query = query.eq('order_status', status)\n    }\n    if (serviceType) {\n      query = query.eq('service_type', serviceType)\n    }\n\n    // 分页\n    const from = (page - 1) * limit\n    const to = from + limit - 1\n    query = query.range(from, to)\n\n    const { data: orders, error, count } = await query\n\n    if (error) {\n      console.error('Database error:', error)\n      return NextResponse.json(\n        { success: false, error: 'Failed to fetch orders' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        orders: orders || [],\n        total: count || 0,\n        page,\n        limit,\n        totalPages: Math.ceil((count || 0) / limit)\n      }\n    })\n    */ } catch (error) {\n        console.error(\"API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// 创建新订单\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        // 提取表单数据\n        const serviceType = formData.get(\"service_type\");\n        const serviceName = formData.get(\"service_name\");\n        const price = parseFloat(formData.get(\"price\"));\n        const customerContact = formData.get(\"customer_contact\");\n        const requirements = formData.get(\"requirements\");\n        // 提取文件\n        const photoFiles = formData.getAll(\"photos\");\n        const styleFiles = formData.getAll(\"style_images\");\n        // 验证必填字段\n        if (!serviceType || !serviceName || !price || !customerContact || !requirements) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // 验证文件\n        const allFiles = [\n            ...photoFiles,\n            ...styleFiles\n        ];\n        for (const file of allFiles){\n            if (file.size > 0) {\n                if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.isValidImageFile)(file)) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: `Invalid file type: ${file.name}`\n                    }, {\n                        status: 400\n                    });\n                }\n                if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.isValidFileSize)(file)) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: `File too large: ${file.name}`\n                    }, {\n                        status: 400\n                    });\n                }\n            }\n        }\n        // 生成订单ID\n        const orderId = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateOrderId)();\n        // 上传照片\n        const photoUrls = [];\n        for (const file of photoFiles){\n            if (file.size > 0) {\n                const fileName = `${orderId}/photos/${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateUUID)()}-${file.name}`;\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.storage.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.STORAGE_BUCKETS.PHOTOS).upload(fileName, file);\n                if (error) {\n                    console.error(\"Photo upload error:\", error);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: \"Failed to upload photos\"\n                    }, {\n                        status: 500\n                    });\n                }\n                const { data: { publicUrl } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.storage.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.STORAGE_BUCKETS.PHOTOS).getPublicUrl(fileName);\n                photoUrls.push(publicUrl);\n            }\n        }\n        // 上传风格图\n        const styleUrls = [];\n        for (const file of styleFiles){\n            if (file.size > 0) {\n                const fileName = `${orderId}/styles/${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateUUID)()}-${file.name}`;\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.storage.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.STORAGE_BUCKETS.STYLE_IMAGES).upload(fileName, file);\n                if (error) {\n                    console.error(\"Style image upload error:\", error);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: \"Failed to upload style images\"\n                    }, {\n                        status: 500\n                    });\n                }\n                const { data: { publicUrl } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.storage.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.STORAGE_BUCKETS.STYLE_IMAGES).getPublicUrl(fileName);\n                styleUrls.push(publicUrl);\n            }\n        }\n        // 创建订单记录\n        const orderData = {\n            order_id: orderId,\n            service_type: serviceType,\n            service_name: serviceName,\n            price: price,\n            customer_contact: customerContact,\n            requirements: requirements,\n            photos: photoUrls,\n            style_images: styleUrls,\n            payment_status: \"pending\",\n            order_status: \"pending\"\n        };\n        const { data: order, error: dbError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.ORDERS).insert(orderData).select().single();\n        if (dbError) {\n            console.error(\"Database insert error:\", dbError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create order\"\n            }, {\n                status: 500\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: {\n                order_id: orderId,\n                order: order\n            }\n        });\n    } catch (error) {\n        console.error(\"Order creation error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/orders/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_BUCKETS: () => (/* binding */ STORAGE_BUCKETS),\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://demo.supabase.co\";\nconst supabaseAnonKey = \"demo_anon_key\";\nconst supabaseServiceKey = \"demo_service_key\";\n// 客户端实例（用于前端操作）\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// 服务端实例（用于管理员操作，拥有更高权限）\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// 数据库表名\nconst TABLES = {\n    ORDERS: \"orders\",\n    ADMIN_USERS: \"admin_users\"\n};\n// 存储桶名\nconst STORAGE_BUCKETS = {\n    PHOTOS: \"customer-photos\",\n    STYLE_IMAGES: \"style-references\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   generateOrderId: () => (/* binding */ generateOrderId),\n/* harmony export */   generateUUID: () => (/* binding */ generateUUID),\n/* harmony export */   isValidFileSize: () => (/* binding */ isValidFileSize),\n/* harmony export */   isValidImageFile: () => (/* binding */ isValidImageFile)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/v4.js\");\n\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// 生成唯一订单ID\nfunction generateOrderId() {\n    const timestamp = Date.now().toString(36);\n    const randomStr = Math.random().toString(36).substring(2, 8);\n    return `ORD-${timestamp}-${randomStr}`.toUpperCase();\n}\n// 生成UUID\nfunction generateUUID() {\n    return (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n}\n// 格式化日期\nfunction formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleString(\"zh-CN\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n        second: \"2-digit\"\n    });\n}\n// 格式化文件大小\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n// 验证文件类型\nfunction isValidImageFile(file) {\n    const validTypes = [\n        \"image/jpeg\",\n        \"image/jpg\",\n        \"image/png\",\n        \"image/webp\"\n    ];\n    return validTypes.includes(file.type);\n}\n// 验证文件大小（默认5MB）\nfunction isValidFileSize(file, maxSizeMB = 5) {\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    return file.size <= maxSizeBytes;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUNMO0FBRTVCLFNBQVNJLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0osdURBQU9BLENBQUNELDBDQUFJQSxDQUFDSztBQUN0QjtBQUVBLFdBQVc7QUFDSixTQUFTQztJQUNkLE1BQU1DLFlBQVlDLEtBQUtDLEdBQUcsR0FBR0MsUUFBUSxDQUFDO0lBQ3RDLE1BQU1DLFlBQVlDLEtBQUtDLE1BQU0sR0FBR0gsUUFBUSxDQUFDLElBQUlJLFNBQVMsQ0FBQyxHQUFHO0lBQzFELE9BQU8sQ0FBQyxJQUFJLEVBQUVQLFVBQVUsQ0FBQyxFQUFFSSxVQUFVLENBQUMsQ0FBQ0ksV0FBVztBQUNwRDtBQUVBLFNBQVM7QUFDRixTQUFTQztJQUNkLE9BQU9iLGdEQUFNQTtBQUNmO0FBRUEsUUFBUTtBQUNELFNBQVNjLFdBQVdDLFVBQWtCO0lBQzNDLE1BQU1DLE9BQU8sSUFBSVgsS0FBS1U7SUFDdEIsT0FBT0MsS0FBS0MsY0FBYyxDQUFDLFNBQVM7UUFDbENDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxLQUFLO1FBQ0xDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxRQUFRO0lBQ1Y7QUFDRjtBQUVBLFVBQVU7QUFDSCxTQUFTQyxlQUFlQyxLQUFhO0lBQzFDLElBQUlBLFVBQVUsR0FBRyxPQUFPO0lBQ3hCLE1BQU1DLElBQUk7SUFDVixNQUFNQyxRQUFRO1FBQUM7UUFBUztRQUFNO1FBQU07S0FBSztJQUN6QyxNQUFNQyxJQUFJbkIsS0FBS29CLEtBQUssQ0FBQ3BCLEtBQUtxQixHQUFHLENBQUNMLFNBQVNoQixLQUFLcUIsR0FBRyxDQUFDSjtJQUNoRCxPQUFPSyxXQUFXLENBQUNOLFFBQVFoQixLQUFLdUIsR0FBRyxDQUFDTixHQUFHRSxFQUFDLEVBQUdLLE9BQU8sQ0FBQyxNQUFNLE1BQU1OLEtBQUssQ0FBQ0MsRUFBRTtBQUN6RTtBQUVBLFNBQVM7QUFDRixTQUFTTSxpQkFBaUJDLElBQVU7SUFDekMsTUFBTUMsYUFBYTtRQUFDO1FBQWM7UUFBYTtRQUFhO0tBQWE7SUFDekUsT0FBT0EsV0FBV0MsUUFBUSxDQUFDRixLQUFLRyxJQUFJO0FBQ3RDO0FBRUEsZ0JBQWdCO0FBQ1QsU0FBU0MsZ0JBQWdCSixJQUFVLEVBQUVLLFlBQW9CLENBQUM7SUFDL0QsTUFBTUMsZUFBZUQsWUFBWSxPQUFPO0lBQ3hDLE9BQU9MLEtBQUtPLElBQUksSUFBSUQ7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbGx1c3RyYXRpb24tYmFja2VuZC8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbi8vIOeUn+aIkOWUr+S4gOiuouWNlUlEXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVPcmRlcklkKCk6IHN0cmluZyB7XG4gIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCkudG9TdHJpbmcoMzYpXG4gIGNvbnN0IHJhbmRvbVN0ciA9IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA4KVxuICByZXR1cm4gYE9SRC0ke3RpbWVzdGFtcH0tJHtyYW5kb21TdHJ9YC50b1VwcGVyQ2FzZSgpXG59XG5cbi8vIOeUn+aIkFVVSURcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZVVVSUQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIHV1aWR2NCgpXG59XG5cbi8vIOagvOW8j+WMluaXpeacn1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZVN0cmluZzogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHJpbmcpXG4gIHJldHVybiBkYXRlLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHtcbiAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgbW9udGg6ICcyLWRpZ2l0JyxcbiAgICBkYXk6ICcyLWRpZ2l0JyxcbiAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgbWludXRlOiAnMi1kaWdpdCcsXG4gICAgc2Vjb25kOiAnMi1kaWdpdCdcbiAgfSlcbn1cblxuLy8g5qC85byP5YyW5paH5Lu25aSn5bCPXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RmlsZVNpemUoYnl0ZXM6IG51bWJlcik6IHN0cmluZyB7XG4gIGlmIChieXRlcyA9PT0gMCkgcmV0dXJuICcwIEJ5dGVzJ1xuICBjb25zdCBrID0gMTAyNFxuICBjb25zdCBzaXplcyA9IFsnQnl0ZXMnLCAnS0InLCAnTUInLCAnR0InXVxuICBjb25zdCBpID0gTWF0aC5mbG9vcihNYXRoLmxvZyhieXRlcykgLyBNYXRoLmxvZyhrKSlcbiAgcmV0dXJuIHBhcnNlRmxvYXQoKGJ5dGVzIC8gTWF0aC5wb3coaywgaSkpLnRvRml4ZWQoMikpICsgJyAnICsgc2l6ZXNbaV1cbn1cblxuLy8g6aqM6K+B5paH5Lu257G75Z6LXG5leHBvcnQgZnVuY3Rpb24gaXNWYWxpZEltYWdlRmlsZShmaWxlOiBGaWxlKTogYm9vbGVhbiB7XG4gIGNvbnN0IHZhbGlkVHlwZXMgPSBbJ2ltYWdlL2pwZWcnLCAnaW1hZ2UvanBnJywgJ2ltYWdlL3BuZycsICdpbWFnZS93ZWJwJ11cbiAgcmV0dXJuIHZhbGlkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKVxufVxuXG4vLyDpqozor4Hmlofku7blpKflsI/vvIjpu5jorqQ1TULvvIlcbmV4cG9ydCBmdW5jdGlvbiBpc1ZhbGlkRmlsZVNpemUoZmlsZTogRmlsZSwgbWF4U2l6ZU1COiBudW1iZXIgPSA1KTogYm9vbGVhbiB7XG4gIGNvbnN0IG1heFNpemVCeXRlcyA9IG1heFNpemVNQiAqIDEwMjQgKiAxMDI0XG4gIHJldHVybiBmaWxlLnNpemUgPD0gbWF4U2l6ZUJ5dGVzXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJ2NCIsInV1aWR2NCIsImNuIiwiaW5wdXRzIiwiZ2VuZXJhdGVPcmRlcklkIiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInRvU3RyaW5nIiwicmFuZG9tU3RyIiwiTWF0aCIsInJhbmRvbSIsInN1YnN0cmluZyIsInRvVXBwZXJDYXNlIiwiZ2VuZXJhdGVVVUlEIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJkYXRlIiwidG9Mb2NhbGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJob3VyIiwibWludXRlIiwic2Vjb25kIiwiZm9ybWF0RmlsZVNpemUiLCJieXRlcyIsImsiLCJzaXplcyIsImkiLCJmbG9vciIsImxvZyIsInBhcnNlRmxvYXQiLCJwb3ciLCJ0b0ZpeGVkIiwiaXNWYWxpZEltYWdlRmlsZSIsImZpbGUiLCJ2YWxpZFR5cGVzIiwiaW5jbHVkZXMiLCJ0eXBlIiwiaXNWYWxpZEZpbGVTaXplIiwibWF4U2l6ZU1CIiwibWF4U2l6ZUJ5dGVzIiwic2l6ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/uuid","vendor-chunks/clsx","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Forders%2Froute&page=%2Fapi%2Forders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Forders%2Froute.ts&appDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwa%2FDesktop%2Fwang%20zhan%2Fillustration-backend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();